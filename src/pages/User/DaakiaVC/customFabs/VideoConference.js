/* eslint-disable */
import React, { useEffect, useState, useRef, useCallback } from "react";
import "../styles/VideoConference.scss";
import {
  isEqualTrackRef,
  isTrackReference,
  isWeb,
  isMobileBrowser,
  log,
} from "@livekit/components-core";
import {
  RoomEvent,
  Track,
  DisconnectReason,
  ConnectionState,
} from "livekit-client";
import { Button } from "antd";
import {
  CarouselLayout,
  FocusLayoutContainer,
  GridLayout,
  LayoutContextProvider,
  RoomAudioRenderer,
  // Chat,
  usePinnedTracks,
  useTracks,
  useCreateLayoutContext,
  // formatChatMessageLinks,
  TrackRefContext,
  Toast,
  MediaDeviceMenu,
} from "@livekit/components-react";
import moment from "moment";
import { ReactComponent as DaakiaLogo } from "./icons/DaakiaLogoLight.svg";
import { ReactComponent as VideoSwitch } from "../assets/icons/videoSwitch.svg";
import {
  constants,
  DataReceivedEvent,
  DrawerState,
  PROCESS_EVENT,
  SocketChannel,
  TOPIC_NAME
} from "../utils/constants";
import { operationHandlers } from '../utils/ai-operations';
import { formatChatMessageLinks } from "../components/chats/ChatsEntry";
import useSocket from "../hooks/useSocket";
import { parseMetadata } from "../utils/helper";

import { ControlBar } from "./ControlBar";
import { ParticipantTile } from "./ParticipantTile";
import { usePictureInPicture } from "./PictureInPicture";

import { MeetindEndedModal } from "../components/settings/MeetindEndingModal";
import { ParticipantList } from "../components/participants/ParticipantList";
import { NotificationModal } from "../components/NotificationModal";
import { VirtualBackgroundDrawer } from "../components/settings/VirtualBackgroundDrawer";
import { ReportProblemDrawer } from "../components/settings/ReportProblemDrawer";
import { RecordingConsentModal } from "../components/RecrodingConsent/RecordingConsentModal";
import BreakoutRoomDrawer from "../components/participants/BreakoutRoomDrawer";
import AllowParticipant from "../components/AllowParticipant";

import recordingStart from "../assets/sounds/recording_start.mp3";
import recordingStop from "../assets/sounds/recording_stop.mp3";
import HostControlDrawer from "../components/settings/HostControlDrawer";
import lobbyNotification from "../assets/sounds/lobbyNotification.mp3";
import LiveCaptionsDrawer from "../components/LiveCaptions/LiveCaptionsDrawer";
import { BreakoutRoomService } from "../services/BreakoutRoomServices";
import { getLocalStorage, setLocalStorage } from "../utils/helper";
import { VideoConferenceService } from "../services/VideoConferenceServices";
import Whiteboard from "../components/whiteboard/Whiteboard";
import ChatsDrawer from "../components/chats/ChatsDrawer";
import { RecordingConsentDrawer } from "../components/RecrodingConsent/RecordingConsentDrawer";
import { ConnectionStateToast } from "./ConnectionStateToast";
import { WhiteboardService } from "../services/WhiteboardServices";
import isElectron from "is-electron";
import PipLayout from "./PipLayout";
import { SettingsMenuServices } from "../services/SettingsMenuServices";
import Joyride from 'react-joyride';
import JoyrideComponent from './JoyrideComponent'
import StatusNotification from "../components/StatusNotification/StatusNotification";
import { generateAvatar } from "../utils/helper";

export function VideoConference({
  room,
  setMovingRoomToken,
  setIsMovingToRoom,
  SettingsComponent,
  isMovingToRoom,
  meetingFeatures,
  isWebinarMode,
  setIsUsingBreakoutRoom,
  isElectronApp,
  screenShareSources,
  isPipWindow,
  isSelfVideoMirrored,
  setIsSelfVideoMirrored,
  deviceIdAudio,
  setDeviceIdAudio,
  deviceIdVideo,
  setDeviceIdVideo,
  ...props
}) {
  const [refresh, setRefresh] = useState(0);
  const [connected, setConnected] = useState(false);
  const [connectionState, setConnectionState] = useState(null);
  const [isDisconneted, setIsDisconnected] = useState(false);
  const [widgetState, setWidgetState] = React.useState({
    showChat: false,
    unreadMessages: 0,
    showSettings: false,
  });
  const [showParticipantsList, setShowParticipantsList] = useState(false);
  const [showHostControl, setShowHostControl] = useState(false);
  const [showBreakoutRoom, setShowBreakoutRoom] = useState(false);
  const [showRaiseHand, setShowRaiseHand] = useState(false);
  const [showEmojiReaction, setShowEmojiReaction] = useState(null);
  const [remoteRaisedHands, setRemoteRaisedHands] = useState(new Map());
  const [notificationVisible, setNotificationVisible] = useState(false);
  const [notificationContent, setNotificationContent] = useState({});
  const [notificationAction, setNotificationAction] = useState("");
  const [remoteEmojiReactions, setRemoteEmojiReactions] = useState(new Map());
  const [showRecording, setShowRecording] = useState(
      props.meetingDetails?.is_recording_active || false
  );
  const [isVBDrawerOpen, setIsVBDrawerOpen] = useState(false);
  const [isRPDrawerOpen, setIsRPDrawerOpen] = useState(false);
  const [isLiveCaptionsDrawerOpen, setIsLiveCaptionsDrawerOpen] =
      useState(false);

  const [toastNotification, setToastNotification] = useState("");
  const [showToast, setShowToast] = useState(false);
  const [toastStatus, setToastStatus] = useState("");
  const [isCoHost, setIsCoHost] = useState(false);
  const [isForceMuteAll, setIsForceMuteAll] = useState(isWebinarMode); // used to disable the mic for all participants
  const [isForceVideoOffAll, setIsForceVideoOffAll] = useState(isWebinarMode); // used to disable the video for all participants
  const [forceMute, setForceMute] = useState(isWebinarMode); // used for host control drawer
  const [forceVideoOff, setForceVideoOff] = useState(isWebinarMode); // used for host control drawer
  const [lobbyParticipants, setLobbyParticipants] = useState(new Map()); // eslint-disable-line no-unused-vars
  const [coHostToken, setCoHostToken] = useState(null);
  const [drawerState, setDrawerState] = useState(null);
  const [breakoutRooms, setBreakoutRooms] = useState({});
  const [roomKeyCounter, setRoomKeyCounter] = useState(1);
  const [currentRoomName, setCurrentRoomName] = useState("");
  const [isBreakoutRoom, setIsBreakoutRoom] = useState(false);
  const [breakoutRoomDuration, setBreakoutRoomDuration] = useState(5);
  const [endBreakoutRoomTimer, setEndBreakoutRoomTimer] = useState(
      breakoutRoomDuration * 60
  );

  const [frontCameraMirroringPreference, setFrontCameraMirroringPreference] = useState(undefined);
  const [isBreakoutRoomCnfigSet, setIsBreakoutRoomCnfigSet] = useState(false);
  const lastAutoFocusedScreenShareTrack = React.useRef(null);
  const [liveCaptionData, setLiveCaptionData] = useState(null);
  const [privatechatparticipants, setPrivateChatParticipants] = useState([]);
  const [selectedPrivateChatParticipant, setSelectedPrivateChatParticipant] =
      useState(null);
  const [showPrivateChat, setShowPrivateChat] = useState(false);
  const [privatechatmessages, setPrivateChatMessages] = useState(new Map());
  const [newMessageRender, setNewMessageRender] = useState(0);
  const [publicChatUnreadMessagesCount, setPublicChatUnreadMessagesCount] =
      useState(0);
  const [privateChatUnreadMessagesCount, setPrivateChatUnreadMessagesCount] =
      useState(0);
  const [liveCaptionsObject, setLiveCaptionsObject] = useState({
    showIcon:
        props.meetingDetails?.transcription_detail?.transcription_enable ||
        props.isHost ||
        isCoHost,
    isLanguageSelected:
        props.meetingDetails?.transcription_detail?.transcription_enable || false,
    langCode:
        props.meetingDetails?.transcription_detail?.transcription_lang_iso ||
        "en-IN",
  });
  const [finalCaptions, setFinalCaptions] = useState([]);

  const [isPIPEnabled, setIsPIPEnabled] = useState(null);
  const [canDownloadChatAttachment, setCanDownloadChatAttachment] =
      useState(false);
  const [sipData, setSipData] = useState(null);
  const [isWhiteboardOpen, setIsWhiteboardOpen] = useState(false);
  const [whiteboardData, setWhiteboardData] = useState({
    elements: [],
    appState: {},
  });
  const [allowLiveCollabWhiteBoard, setAllowLiveCollabWhiteBoard] =
      useState(false);
  const [publicChatMessages, setPublicChatMessages] = useState([]);
  const [isExitWhiteboardModalOpen, setIsExitWhiteboardModalOpen] =
      useState(false);
  const [whiteboardSceneData, setWhiteboardSceneData] = useState({
    start: 0,
    end: 0,
  });
  const [whiteboardId, setWhiteboardId] = useState(null);
  const [isPinned, setIsPinned] = useState(false);
  const [screenShareDisplayId, setScreenShareDisplayId] = useState(1);
  // const [isAnnotationEnabled, setIsAnnotationEnabled] = useState(false);
  const [translationDetails, setTranslationDetails] = useState({
    source_lang: undefined,
    target_lang: "",
    text_cap: "",
  });
  const [finalTranslatedCaptions, setFinalTranslatedCaptions] = useState([]);
  // check if screen share is enabled
  const [isScreenShareEnabled, setIsScreenShareEnabled] = useState(false);
  const [screenShareMode, setScreenShareMode] = useState("text");
  const [isFocusTrackEnabled, setIsFocusTrackEnabled] = useState(false);

  // live captions tour
  const [liveCapTourState, setLiveCapTourState] = useState({
    run: false,
    sidebarOpen: false,
    stepIndex: 0,
    steps: [],
  });
  const [isRecordingLoading, setIsRecordingLoading] = useState(false);

  // Recording Consent
  const [isRecordingConsentModalOpen, setIsRecordingConsentModalOpen] = useState(false);
  const [participantConsent, setParticipantConsent] = useState([]);
  const [showRecordingConsentDrawer, setShowRecordingConsentDrawer] = useState(false);
  const [showRecordingConsentIcon, setShowRecordingConsentIcon] = useState(false);



  const tracks = useTracks(
      [
        { source: Track.Source.Camera, withPlaceholder: true },
        { source: Track.Source.ScreenShare, withPlaceholder: false },
      ],
      { updateOnlyOn: [RoomEvent.ActiveSpeakersChanged], onlySubscribed: false }
  );

  const widgetUpdate = (state) => {
    log.debug("updating widget state", state);
    setWidgetState(state);
  };

  const onScreenShareChange = useCallback(
      (enabled) => {
        setIsScreenShareEnabled(enabled);
      },
      [setIsScreenShareEnabled]
  );

  const layoutContext = useCreateLayoutContext();

  const screenShareTracks = tracks
      .filter(isTrackReference)
      .filter((track) => track.publication.source === Track.Source.ScreenShare);

  const focusTrack = usePinnedTracks(layoutContext)?.[0];
  const carouselTracks = tracks.filter(
      (track) => !isEqualTrackRef(track, focusTrack)
  );

  // Socket connection

  const { socket, isSocketConnected } = useSocket({
    keepConnected: isWhiteboardOpen,
    token: props.token,
    connectionUrl: constants.SOCKET_URL,
    user: room?.localParticipant?.participantInfo,
  });

  // Get breakout room details
  // For any bugs in breakout room details, first check this function for any issues, as this is the main function for all breakout room state management
  const fetchBreakoutRoomDetails = async () => {
    setTimeout(async () => {
      try {
        if (!room) return;
        const response = await BreakoutRoomService.getBreakoutRoomDetail(
            props.id,
            coHostToken,
            room?.localParticipant?.participantInfo
        );
        if (response?.success === 0) {
          console.log("Error getting breakout room details", response);
          return;
        }

        if (response?.data.length > 0) {
          setBreakoutRooms((prevRooms) => {
            const updatedRooms = { ...prevRooms };
            const allKeys = [];
            // Push all the keys of the existing rooms into allKeys
            Object.keys(updatedRooms).forEach((key) => {
              if (updatedRooms[key].manual) {
                allKeys.push(key);
              }
            });
            response.data.forEach((r) => {
              const roomId = r?.name.split("__BR")[1];
              const key = roomId || "0";
              const roomName = roomId ? `Breakout Room ${key}` : "Main Room";
              const participants = r?.participants;

              allKeys.push(key);

              if (updatedRooms[key]) {
                // Update participants of the existing room
                updatedRooms[key].participants = participants;
                // delete updatedRooms[key].manual;
              } else {
                // Add new room
                updatedRooms[key] = {
                  name: roomName,
                  participants,
                  key,
                };
              }
            });

            for (let key in updatedRooms) {
              // Check if the key should be deleted
              if (
                  !allKeys.includes(key) && // Key not in allKeys array
                  (updatedRooms[key].isDeleted ||
                      !updatedRooms[key].manual || // Manual is false
                      !("manual" in updatedRooms[key])) && // or manual is not present
                  key !== "0" // Key is not "0"
              ) {
                delete updatedRooms[key];
              }
            }
            const activeRooms = Object.keys(updatedRooms).filter(
                (key) =>
                    !updatedRooms[key].isDeleted ||
                    !("isDeleted" in updatedRooms[key])
            );
            if (activeRooms.length <= 1 && showBreakoutRoom) {
              setDrawerState(DrawerState.PARTICIPANTS);
              setIsBreakoutRoomCnfigSet(false);
            }
            // Ensure the main room is always present
            if (!updatedRooms["0"]) {
              updatedRooms["0"] = {
                name: "Main Room",
                participants: [],
                key: "0",
              };
            }
            // Get the maximum value from the allKeys array
            const maxKey = Math.max(...allKeys);

            setRoomKeyCounter(maxKey + 1);
            return updatedRooms;
          });
        }
      } catch (error) {
        setToastNotification(error.message);
        setToastStatus("error");
        setShowToast(true);
        // console.log("Error getting breakout room details", error);
      }
    }, 500);
  };

  // Poll breakout room details
  useEffect(() => {
    if (!room || room?.state !== "connected") return;
    // Define a function to start polling
    const startPolling = () => {
      // Set interval for polling every 15 seconds
      const intervalId = setInterval(fetchBreakoutRoomDetails, 10000);

      // Cleanup function to clear the interval
      return () => clearInterval(intervalId);
    };

    let stopPolling; // Variable to hold the cleanup function for polling
    if (
        room?.localParticipant?.metadata &&
        (parseMetadata(room?.localParticipant?.metadata)?.role_name ===
            "moderator" ||
            parseMetadata(room?.localParticipant?.metadata)?.role_name === "cohost") &&
        (showBreakoutRoom || drawerState === DrawerState.PARTICIPANTS) &&
        Object.keys(breakoutRooms).length > 1
    ) {
      stopPolling = startPolling(); // Start polling and keep the cleanup function
    }
    if (
        room?.localParticipant?.metadata &&
        parseMetadata(room?.localParticipant?.metadata)?.role_name === "moderator" &&
        Object.keys(breakoutRooms).length > 1
    ) {
      fetchBreakoutRoomDetails();
      room.on(RoomEvent.ParticipantConnected, fetchBreakoutRoomDetails);
      room.on(RoomEvent.ParticipantDisconnected, fetchBreakoutRoomDetails);
    }

    if (
        parseMetadata(room?.localParticipant?.metadata)?.role_name === "cohost" &&
        coHostToken &&
        coHostToken !== "" &&
        Object.keys(breakoutRooms).length > 1
    ) {
      fetchBreakoutRoomDetails();
      room.on(RoomEvent.ParticipantConnected, fetchBreakoutRoomDetails);
      room.on(RoomEvent.ParticipantDisconnected, fetchBreakoutRoomDetails);
    }

    // Cleanup on unmount or when dependencies change
    return () => {
      room?.off(RoomEvent.ParticipantConnected, fetchBreakoutRoomDetails);
      room?.off(RoomEvent.ParticipantDisconnected, fetchBreakoutRoomDetails);
      if (stopPolling) stopPolling(); // Call the cleanup function for polling
    };
  }, [
    room,
    room?.localParticipant?.metadata,
    showBreakoutRoom,
    coHostToken,
    drawerState,
    Object.keys(breakoutRooms).length,
  ]);

  // Handle Event for participant  disconnected
  useEffect(() => {
    if (!room) return;

    const onParticipantDisconnected = (participant) => {
      // Find the participant in the list
      const participantIndex = privatechatparticipants.findIndex((part) => {
        return (
            part.participant.identity.toString() ===
            participant.identity.toString()
        );
      });

      if (participantIndex >= 0) {
        // Create a new array with the updated participant's isConnected status
        const updatedParticipants = [...privatechatparticipants];
        updatedParticipants[participantIndex] = {
          ...updatedParticipants[participantIndex],
          isConnected: false,
        };

        // Update the state with the new array
        setPrivateChatParticipants(updatedParticipants);

        // Update selectedPrivateChatParticipant if it matches the disconnected participant
        if (
            selectedPrivateChatParticipant &&
            selectedPrivateChatParticipant.participant.identity.toString() ===
            participant.identity.toString()
        ) {
          // Create a new object for the selected participant
          const updatedSelectedParticipant = {
            ...selectedPrivateChatParticipant,
            isConnected: false,
          };

          setSelectedPrivateChatParticipant(updatedSelectedParticipant);
        }
      }
    };

    room.on(RoomEvent.ParticipantDisconnected, onParticipantDisconnected);

    return () => {
      room.off(RoomEvent.ParticipantDisconnected, onParticipantDisconnected);
    };
  }, [room, privatechatparticipants, selectedPrivateChatParticipant]);



const [pipControlPosition, setPipControlPosition] = useState('bottom');

// Expose functions for testing and user control
useEffect(() => {
  window.togglePipControlPosition = () => {
    setPipControlPosition(prev => prev === 'top' ? 'bottom' : 'top');
    console.log('PiP control position toggled to:', pipControlPosition === 'top' ? 'bottom' : 'top');
  };

  // Test function for automatic PiP
  window.testAutoPip = () => {
    console.log('=== AUTOMATIC PIP TEST ===');
    console.log('Browser Support:', {
      documentPiP: 'documentPictureInPicture' in window,
      mediaSession: 'mediaSession' in navigator,
      enterpictureinpicture: navigator.mediaSession && 'setActionHandler' in navigator.mediaSession
    });
    console.log('Current Status:', {
      isPipSupported,
      isAutoPipSupported,
      hasActiveMediaCapture,
      isAutoPipEnabled,
      localParticipant: room?.localParticipant?.identity,
      cameraEnabled: room?.localParticipant?.isCameraEnabled,
      micEnabled: room?.localParticipant?.isMicrophoneEnabled
    });
    console.log('Instructions:');
    console.log('1. Make sure camera or microphone is enabled');
    console.log('2. Switch to another tab');
    console.log('3. Automatic PiP should open');
    console.log('4. Switch back to this tab');
    console.log('5. Automatic PiP should close');
  };

  return () => {
    delete window.togglePipControlPosition;
    delete window.testAutoPip;
  };
}, [pipControlPosition, isPipSupported, isAutoPipSupported, hasActiveMediaCapture, isAutoPipEnabled, room?.localParticipant]);

// Use the PiP hook
const {
  togglePipMode,
  pipPortal,
  isSupported: isPipSupported,
  isAutoPipSupported,
  hasActiveMediaCapture,
  isAutoPipEnabled
} = usePictureInPicture({
  setIsPIPEnabled,
  setToastNotification,
  setToastStatus,
  setShowToast,
  localParticipant: room?.localParticipant,
  room: room,
  controlPosition: pipControlPosition,
  isHost: props.isHost,
  isCoHost: isCoHost,
});

// Debug logging for automatic PiP
useEffect(() => {
  console.log('PiP Status:', {
    isPipSupported,
    isAutoPipSupported,
    hasActiveMediaCapture,
    isAutoPipEnabled,
    localParticipant: room?.localParticipant?.identity
  });
}, [isPipSupported, isAutoPipSupported, hasActiveMediaCapture, isAutoPipEnabled, room?.localParticipant]);



// Handle PiP state changes
useEffect(() => {
  if (isPIPEnabled === null) return;
  togglePipMode(isPIPEnabled);
}, [isPIPEnabled, togglePipMode]);

  // Handle Event for participant  connected
  useEffect(() => {
    if (!room) return;

    const onParticipantConnected = (participant) => {
      // Find the participant in the list
      const participantIndex = privatechatparticipants.findIndex((part) => {
        return (
            part.participant.identity.toString() ===
            participant.identity.toString()
        );
      });

      if (participantIndex >= 0) {
        // Create a new array with the updated participant's isConnected status
        const updatedParticipants = [...privatechatparticipants];
        updatedParticipants[participantIndex] = {
          ...updatedParticipants[participantIndex],
          isConnected: true,
        };

        // Update the state with the new array
        setPrivateChatParticipants(updatedParticipants);

        // Update selectedPrivateChatParticipant if it matches the disconnected participant
        if (
            selectedPrivateChatParticipant &&
            selectedPrivateChatParticipant.participant.identity.toString() ===
            participant.identity.toString()
        ) {
          // Create a new object for the selected participant
          const updatedSelectedParticipant = {
            ...selectedPrivateChatParticipant,
            isConnected: true,
          };

          setSelectedPrivateChatParticipant(updatedSelectedParticipant);
        }
      }
      // if (
      //   liveCaptionsObject.isLanguageSelected &&
      //   liveCaptionsObject.showIcon
      // ) {
      //   const encoder = new TextEncoder();
      //   const data = encoder.encode(
      //     JSON.stringify({
      //       action: DataReceivedEvent.SHOW_LIVECAPTION,
      //       livecaptionsdata: liveCaptionsObject,
      //     })
      //   );
      //   room?.localParticipant.publishData(data, {
      //     reliable: true,
      //     destinationIdentities: [participant.identity],
      //   });
      // }
    };

    room.on(RoomEvent.ParticipantConnected, onParticipantConnected);

    return () => {
      room.off(RoomEvent.ParticipantConnected, onParticipantConnected);
    };
  }, [room, privatechatparticipants, selectedPrivateChatParticipant]);

  // Check if meeting is finished
  useEffect(() => {
    if (!room || meetingFeatures?.is_basic === 0) return;
    const endDateTime = moment(props.meetingDetails?.end_date);
    const intervalId = setInterval(() => {
      const currentDateTime = moment();
      const timeDifference = endDateTime.diff(currentDateTime, "seconds");

      if (timeDifference <= 0) {
        props.setIsMeetingFinished(() => true);
        setLocalStorage(constants.CO_HOST_TOKEN, null);
        room.disconnect();
        if (isElectronApp) {
          window?.electronAPI?.ipcRenderer?.send("stop-annotation");
        }
      } else if (timeDifference <= 300 && timeDifference >= 280) {
        // 5 minutes in seconds
        setToastNotification(`Meeting will end in 5 minutes.`);
        setToastStatus("warning");
        setShowToast(true);
      }
    }, 30000); // Run every 30sec

    return () => clearInterval(intervalId); // Cleanup interval on unmount
  }, [room]);

  useEffect(() => {
    // If screen share tracks are published, and no pin is set explicitly, auto set the screen share.
    if (
        screenShareTracks.some((track) => track.publication.isSubscribed) &&
        lastAutoFocusedScreenShareTrack.current === null
    ) {
      log.debug("Auto set screen share focus:", {
        newScreenShareTrack: screenShareTracks[0],
      });
      layoutContext.pin.dispatch?.({
        msg: "set_pin",
        trackReference: screenShareTracks[0],
      });
      lastAutoFocusedScreenShareTrack.current = screenShareTracks[0]; // eslint-disable-line
      if (isElectronApp && isScreenShareEnabled) {
        let userChoice = getLocalStorage(constants.MEETING_USER_CHOICES);
        window?.electronAPI?.ipcRenderer?.send("minimize-main-window", {
          video: userChoice.video,
          audio: userChoice.audio,
          screenShareDisplayId,
        });
      }
    } else if (
        lastAutoFocusedScreenShareTrack.current &&
        !screenShareTracks.some(
            (track) =>
                track.publication.trackSid ===
                lastAutoFocusedScreenShareTrack.current?.publication?.trackSid
        )
    ) {
      log.debug("Auto clearing screen share focus.");
      layoutContext.pin.dispatch?.({ msg: "clear_pin" });
      lastAutoFocusedScreenShareTrack.current = null;
      if (isElectronApp) {
        window?.electronAPI?.ipcRenderer?.send("stop-annotation");
      }
    }
    if (focusTrack && !isTrackReference(focusTrack)) {
      const updatedFocusTrack = tracks.find(
          (tr) =>
              tr.participant.identity === focusTrack.participant.identity &&
              tr.source === focusTrack.source
      );
      if (
          updatedFocusTrack !== focusTrack &&
          isTrackReference(updatedFocusTrack)
      ) {
        layoutContext.pin.dispatch?.({
          msg: "set_pin",
          trackReference: updatedFocusTrack,
        });
      }
    }
  }, [
    screenShareTracks
        .map(
            (ref) => `${ref.publication.trackSid}_${ref.publication.isSubscribed}`
        )
        .join(),
    focusTrack?.publication?.trackSid,
    tracks,
  ]);

  useEffect(() => {
    if (!room) return;
    if (!connected) return;
    const encoder = new TextEncoder();
    const data = encoder.encode(
        JSON.stringify({
          action: showRaiseHand
              ? DataReceivedEvent.RAISE_HAND
              : DataReceivedEvent.STOP_RAISE_HAND,
        })
    );
    room.localParticipant.publishData(data, { reliable: true });
  }, [showRaiseHand, room, connected]);

  useEffect(() => {
    if (!room || !connected || !showEmojiReaction) return;
    const encoder = new TextEncoder();
    const data = encoder.encode(
        JSON.stringify({
          action: showEmojiReaction,
        })
    );
    room.localParticipant.publishData(data, { reliable: true });
  }, [showEmojiReaction, room, connected]);

  // Data Received
  useEffect(() => {
    if (!room) return;
    const decoder = new TextDecoder();
    const onDataReceived = async (payload, participant, kind) => {
      const strData = decoder.decode(payload);
      const data = JSON.parse(strData);
      if (data.action && data.action === DataReceivedEvent.RAISE_HAND) {
        setRemoteRaisedHands((prev) =>
            new Map(prev).set(participant.identity, true)
        );
      } else if (
          data.action &&
          data.action === DataReceivedEvent.STOP_RAISE_HAND
      ) {
        setRemoteRaisedHands((prev) => {
          const newMap = new Map(prev);
          newMap.delete(participant?.identity);
          return newMap;
        });
      } else if (
          data.action &&
          data.action === DataReceivedEvent.STOP_RAISE_HAND_ALL
      ) {
        setRemoteRaisedHands(new Map());
        setShowRaiseHand(false);
      } else if (
          data.action &&
          data.action === DataReceivedEvent.ASK_TO_UNMUTE_MIC
      ) {
        // Ask to unmute mic
        setNotificationContent({
          title: `${participant.name} is asking you to turn on your mic`,
        });
        setNotificationAction("unmute");
        setNotificationVisible(true);
        if (isForceMuteAll) {
          setIsForceMuteAll(false);
        }
      } else if (data.action && data.action === DataReceivedEvent.MUTE_MIC) {
        room.localParticipant.setMicrophoneEnabled(false);
        setToastNotification("Microphone muted!");
        setToastStatus("info");
        setShowToast(true);
        // Mute mic
      } else if (
          data.action &&
          data.action === DataReceivedEvent.ASK_TO_UNMUTE_CAMERA
      ) {
        // Ask to unmute camera
        setNotificationContent({
          title: `${participant.name} is asking you to turn on your camera`,
        });
        setNotificationAction("videoOn");
        setNotificationVisible(true);
        if (isForceVideoOffAll) {
          setIsForceVideoOffAll(false);
        }
      } else if (data.action && data.action === DataReceivedEvent.MUTE_CAMERA) {
        room.localParticipant.setCameraEnabled(false);
        setToastNotification("Camera off!");
        setToastStatus("info");
        setShowToast(true);
      } else if (
          data.action &&
          data.action === DataReceivedEvent.SEND_PRIVATE_MESSAGE
      ) {
        const { message, id, timestamp, isReplied, replyMessage } = data;
        // Find the participant in the private chat message map using identity
        const from = privatechatmessages.get(participant.identity);
        if (!from) {
          privatechatmessages.set(participant.identity, [
            {
              from: participant,
              message,
              id,
              timestamp,
              isReplied,
              replyMessage,
            },
          ]);
        } else {
          from.push({
            from: participant,
            message,
            id,
            timestamp,
            isReplied,
            replyMessage,
          });
        }
        setPrivateChatMessages(privatechatmessages);

        if (privatechatparticipants.length === 0) {
          setSelectedPrivateChatParticipant({
            key: 1,
            participant,
            isConnected: true,
            receivedUnreadMessagesCount: 1,
          });
          setPrivateChatParticipants([
            {
              key: 1,
              participant,
              isConnected: true,
              receivedUnreadMessagesCount: 1,
            },
          ]);
          setPrivateChatUnreadMessagesCount((prev) => prev + 1);
        } else {
          const participantIndex = privatechatparticipants.findIndex((part) => {
            return part.participant.identity === participant.identity;
          });
          if (participantIndex < 0) {
            const maxKey = privatechatparticipants.reduce(
                (max, p) => Math.max(max, p.key),
                0
            );
            const newParticipant = {
              key: maxKey + 1,
              participant,
              isConnected: true,
              receivedUnreadMessagesCount: 1,
            };
            setPrivateChatParticipants([
              ...privatechatparticipants,
              newParticipant,
            ]);
            setPrivateChatUnreadMessagesCount((prev) => prev + 1);
          } else {
            const updatedParticipants = [...privatechatparticipants];

            if (
                selectedPrivateChatParticipant?.participant.identity !==
                participant.identity
            ) {
              updatedParticipants[participantIndex] = {
                ...updatedParticipants[participantIndex],
                receivedUnreadMessagesCount:
                    updatedParticipants[participantIndex]
                        .receivedUnreadMessagesCount + 1,
              };
              setPrivateChatUnreadMessagesCount((prev) => prev + 1);
            } else if (!showPrivateChat) {
              const updatedSelectedParticipant = {
                ...selectedPrivateChatParticipant,
                receivedUnreadMessagesCount:
                    selectedPrivateChatParticipant.receivedUnreadMessagesCount +
                    1,
              };
              setSelectedPrivateChatParticipant(updatedSelectedParticipant);

              updatedParticipants[participantIndex] = {
                ...updatedParticipants[participantIndex],
                receivedUnreadMessagesCount:
                    updatedParticipants[participantIndex]
                        .receivedUnreadMessagesCount + 1,
              };
              setPrivateChatUnreadMessagesCount((prev) => prev + 1);
            }

            setPrivateChatParticipants(updatedParticipants);
          }
        }

        setNewMessageRender((prev) => prev + 1);
      } else if (
          data.action &&
          data.action === DataReceivedEvent.SEND_PUBLIC_MESSAGE
      ) {
        const { message, id, timestamp, isReplied, replyMessage } = data;
        const mesg = {
          id: id,
          message: message,
          timestamp: timestamp,
          from: participant,
          isReplied,
          replyMessage,
        };
        setPublicChatMessages((prev) => [...prev, mesg]);
        setPublicChatUnreadMessagesCount((prev) => prev + 1);
      } else if (data.action && data.action === DataReceivedEvent.HEART) {
        setRemoteEmojiReactions((prev) =>
            new Map(prev).set(participant.identity, DataReceivedEvent.HEART)
        );
        // console.log(remoteEmojiReactions);
      } else if (data.action && data.action === DataReceivedEvent.BLUSH) {
        setRemoteEmojiReactions((prev) =>
            new Map(prev).set(participant.identity, DataReceivedEvent.BLUSH)
        );
        // console.log(remoteEmojiReactions);
      } else if (data.action && data.action === DataReceivedEvent.CLAP) {
        setRemoteEmojiReactions((prev) =>
            new Map(prev).set(participant.identity, DataReceivedEvent.CLAP)
        );
        // console.log(remoteEmojiReactions);
      } else if (data.action && data.action === DataReceivedEvent.SMILE) {
        setRemoteEmojiReactions((prev) =>
            new Map(prev).set(participant.identity, DataReceivedEvent.SMILE)
        );
        // console.log(remoteEmojiReactions);
      } else if (data.action && data.action === DataReceivedEvent.THUMBS_UP) {
        setRemoteEmojiReactions((prev) =>
            new Map(prev).set(participant.identity, DataReceivedEvent.THUMBS_UP)
        );
      } else if (
          data.action &&
          data.action === DataReceivedEvent.GRINNING_FACE
      ) {
        setRemoteEmojiReactions((prev) =>
            new Map(prev).set(
                participant.identity,
                DataReceivedEvent.GRINNING_FACE
            )
        );
      } else if (
          data.action &&
          data.action === DataReceivedEvent.MAKE_CO_HOST
      ) {
        setForceMute(data?.forceMute ?? forceMute);
        setForceVideoOff(data?.forceVideoOff ?? forceVideoOff);
        setCoHostToken(data.token);
        const tokenDetails = {
          token: data.token,
          meetingId: props.id,
          meeting_attendance_id: parseMetadata(room.localParticipant.metadata)
              ?.meeting_attendance_id,
          current_session_uid: parseInt(
              parseMetadata(room.localParticipant.metadata)?.current_session_uid,
              10
          ),
        };
        setLocalStorage(constants.CO_HOST_TOKEN, tokenDetails);
        setBreakoutRooms(data?.breakoutRooms ?? breakoutRooms);
        setAllowLiveCollabWhiteBoard(
            data?.allowLiveCollabWhiteBoard ?? allowLiveCollabWhiteBoard
        );
      } else if (
          data.action &&
          data.action === DataReceivedEvent.REMOVE_CO_HOST
      ) {
        setCoHostToken(null);
        setLocalStorage(constants.CO_HOST_TOKEN, null);
      } else if (data.action && data.action === DataReceivedEvent.LOBBY) {
        if (
            props.isHost ||
            parseMetadata(room.localParticipant.metadata)?.role_name === "cohost"
        ) {
          setLobbyParticipants((prev) => {
            const updatedMap = new Map(prev);
            const currentTime = Date.now();

            if (updatedMap.has(data.request_id)) {
              const existingData = updatedMap.get(data.request_id);
              updatedMap.set(data.request_id, {
                ...existingData,
                time: currentTime,
              });
            } else {
              updatedMap.set(data.request_id, { ...data, time: currentTime });
              // setToastNotification(`${data.display_name} wants to join`);
              setToastStatus("content");
              setToastNotification(
                <AllowParticipant
                  participantName={data.display_name}
                  setLobbyParticipants={setLobbyParticipants}
                  lobbyParticipants={lobbyParticipants}
                  requestId={data.request_id}
                  id={props.id}
                  setShowToast={setShowToast}
                  coHostToken={coHostToken}
                  localParticipant={room?.localParticipant}
                />
              );
              setShowToast(true);
              new Audio(lobbyNotification).play();
            }

            return updatedMap;
          });
        }
      } else if (
          data.action &&
          data.action === DataReceivedEvent.FORCE_MUTE_ALL
      ) {
        if (
            parseMetadata(room.localParticipant.metadata)?.role_name !== "cohost" &&
            parseMetadata(room.localParticipant.metadata)?.role_name !== "moderator"
        ) {
          room.localParticipant.setMicrophoneEnabled(false);
          setIsForceMuteAll(data.value);
        }
        setForceMute(data.value);
      } else if (
          data.action &&
          data.action === DataReceivedEvent.FORCE_VIDEO_OFF_ALL
      ) {
        if (
            parseMetadata(room.localParticipant.metadata)?.role_name !== "cohost" &&
            parseMetadata(room.localParticipant.metadata)?.role_name !== "moderator"
        ) {
          room.localParticipant.setCameraEnabled(false);
          setIsForceVideoOffAll(data.value);
        }
        setForceVideoOff(data.value);
      } else if (
          data.action &&
          data.action === DataReceivedEvent.BR_MOVE_PARTICIPANT
      ) {
        try {
          if (data.participant_id.includes(room.localParticipant.identity)) {
            const response = await BreakoutRoomService.getBreakoutRoomToken(
                room.localParticipant,
                `${data?.to_meeting_uid}`
            );
            if (response?.success === 0) {
              console.log("Error getting breakout room token", response);
              return;
            }
            if (response?.data?.access_token) {
              setIsUsingBreakoutRoom(true);
              setIsMovingToRoom(true);
              setMovingRoomToken(response?.data?.access_token?.token);
              if (
                  parseMetadata(room?.localParticipant?.metadata)?.role_name ===
                  "moderator" ||
                  parseMetadata(room?.localParticipant?.metadata)?.role_name ===
                  "cohost"
              ) {
                await fetchBreakoutRoomDetails();
              }
            }
          }
        } catch (error) {
          setToastNotification(error.message);
          setToastStatus("error");
          setShowToast(true);
          // console.log("Error getting breakout room token", error);
        }
      } else if (
          data.action &&
          data.action === DataReceivedEvent.BREAKOUT_ROOM_UPDATE
      ) {
        if (
            parseMetadata(room?.localParticipant?.metadata)?.role_name ===
            "moderator" ||
            parseMetadata(room?.localParticipant?.metadata)?.role_name === "cohost"
        ) {
          let activeRoom = 0;
          setBreakoutRooms(data.breakoutRooms);
          if (Object.keys(data.breakoutRooms).length <= 2) {
            // Use Object.keys to iterate only over own properties
            Object.keys(data.breakoutRooms).forEach((key) => {
              if (!data.breakoutRooms[key].isDeleted) {
                activeRoom += 1;
              }
            });
            if (activeRoom <= 1) {
              setDrawerState(DrawerState.PARTICIPANTS);
              setIsBreakoutRoomCnfigSet(false);
            }
          }
        }
      } else if (data.action && data.action === DataReceivedEvent.LIVECAPTION) {
        setLiveCaptionData(data);
        setLiveCaptionsObject((prev) => ({
          ...prev,
          showIcon: true,
        }));
      } else if (
          data.action &&
          data.action === DataReceivedEvent.SHOW_LIVECAPTION
      ) {
        // Data Received for live caption
        setLiveCaptionsObject(prev => ({
          ...prev,
          showIcon: data.liveCaptionsData.showIcon,
          isLanguageSelected: prev.isLanguageSelected || data.liveCaptionsData.isLanguageSelected,
          langCode: data.liveCaptionsData.langCode,
        }));
        setDrawerState(DrawerState.LIVECAPTION);
        setToastNotification("Live caption is enabled");
        setToastStatus("info");
        setShowToast(true);
        setToastStatus("success");
      } else if (
          data.action &&
          data.action === DataReceivedEvent.REQUEST_LIVECAPTION_DRAWER_STATE
      ) {
        if (liveCaptionsObject.showIcon) {
          const encoder = new TextEncoder();
          const data = encoder.encode(
              JSON.stringify({
                action: DataReceivedEvent.SHOW_LIVECAPTION,
                liveCaptionsData: { ...liveCaptionsObject },
              })
          );
          room?.localParticipant.publishData(data, {
            reliable: true,
            destinationIdentities: [participant?.identity],
          });
        }
      } else if (
          data.action &&
          data.action === DataReceivedEvent.CAN_DOWNLOAD_CHAT_ATTACHEMENT
      ) {
        setCanDownloadChatAttachment(data.value);
      } else if (
          data.action &&
          data.action === DataReceivedEvent.WHITEBOARD_STATE
      ) {
        setToastNotification("Whiteboard Opened");
        setShowToast(true);
        setToastStatus("success");
        setIsWhiteboardOpen(data?.value);
      } else if (
          data.action &&
          data.action === DataReceivedEvent.ALLOW_LIVE_COLLAB_WHITEBOARD
      ) {
        setAllowLiveCollabWhiteBoard(data.value);
      } else if (
          data.action &&
          data.action === DataReceivedEvent.WHITEBOARD_DATA_REQUEST
      ) {
        if (isSocketConnected) {
          setTimeout(() => {
            socket.emit(SocketChannel.WHITEBOARD_UPDATE, {
              elements: whiteboardData?.elements,
            });
          }, 500); // 500 milliseconds delay
        }
      }else if(data.action && data.action === DataReceivedEvent.RECORDING_CONSENT_MODAL){
        setIsRecordingConsentModalOpen(data.value);
      }else if (data.action && data.action === DataReceivedEvent.RECORDING_CONSENT_STATUS){
        // Find the participant in the participantConsent array
        const participantIndex = participantConsent.findIndex(p => p.participantId === participant?.identity);

        if (participantIndex !== -1) {
          // Update existing participant's consent status
          const updatedConsent = [...participantConsent];
          updatedConsent[participantIndex] = {
            ...updatedConsent[participantIndex],
            consent: data.consent
          };
          setParticipantConsent(updatedConsent);
        } else {
          // Add new participant with consent status
          setParticipantConsent(prev => [...prev, {
            participantName: participant?.name,
            participantId: participant?.identity,
            consent: data.consent
          }]);
        }
      } else if (
        data.action &&
        data.action === DataReceivedEvent.SCREEN_CAPTURE_TAKEN
      ) {
        // Handle screen capture notification
        const capturerName = data.participantName || participant?.name || participant?.identity || 'Someone';

        console.log("Screen capture taken by:", capturerName);

        // Show toast notification to all participants
        setToastNotification(`${capturerName} captured the screenshot`);
        setToastStatus("info");
        setShowToast(true);
      }
    };
    room.on(RoomEvent.DataReceived, onDataReceived);
    return () => {
      room.off(RoomEvent.DataReceived, onDataReceived);
    };
  }, [
    room,
    isCoHost,
    coHostToken,
    lobbyParticipants,
    privatechatmessages,
    privateChatUnreadMessagesCount,
    selectedPrivateChatParticipant,
    whiteboardData,
    isSocketConnected,
    socket,
    liveCaptionsObject,
    participantConsent
  ]);

  // AI Agent use Effect for operations
  useEffect(() => {
    if (!room || room?.state !== "connected") return;

    const handleTextStream = async (reader, participantInfo) => {
      try {
        const message = await reader.readAll();

        // Get the handler for the operation type
        const handler = operationHandlers[message];

        if (handler) {
          handler(room, setToastNotification, setToastStatus, setShowToast);
        }
      } catch (error) {
        console.log("error", error);
        // setToastNotification("Error processing operation");
        // setToastStatus("error");
        // setShowToast(true);
      }
    };

    // Only register the handler if the method exists
    if (typeof room?.registerTextStreamHandler === 'function') {
      // Register the handler for the operation topic
      room.registerTextStreamHandler(TOPIC_NAME.AGENT_OPERATION, handleTextStream);

      // Cleanup function to unregister the handler
      return () => {
        if (typeof room?.unregisterTextStreamHandler === 'function') {
          room.unregisterTextStreamHandler(TOPIC_NAME.AGENT_OPERATION);
        }
      };
    }
  }, [room,room?.state]);

  // Mic and Camera control

  useEffect(() => {
    if (isElectronApp) {
      window.electronAPI.ipcRenderer.on("track-control", (e, data) => {
        if (data.action === PROCESS_EVENT.MIC) {
          room.localParticipant.setMicrophoneEnabled(data.value);
        } else if (data.action === PROCESS_EVENT.CAMERA) {
          room.localParticipant.setCameraEnabled(data.value);
        }
      });
    }
  }, []);

  // Co-host metadata?.role_name change
  useEffect(() => {
    if (!room) return;
    const onParticipantMetadataChanged = (_, participant) => {
      if (
          parseMetadata(participant.metadata)?.role_name === "cohost" &&
          room.localParticipant.identity === participant.identity
      ) {
        setIsCoHost(true);
        setLiveCaptionsObject((prev) => ({
          ...prev,
          showIcon: true,
        }));
      } else if (
          parseMetadata(participant.metadata)?.role_name === "participant" &&
          room.localParticipant.identity === participant.identity
      ) {
        setIsCoHost(false);
      }
    };
    room.on(RoomEvent.ParticipantMetadataChanged, onParticipantMetadataChanged);
    return () => {
      room.off(
          RoomEvent.ParticipantMetadataChanged,
          onParticipantMetadataChanged
      );
    };
  }, [room]);

  // Connection state change
  useEffect(() => {
    if (!room) return;
    const onConnectionStateChange = (state) => {
      setConnected(state === ConnectionState.Connected);
      setConnectionState(state);
    };
    room.on(RoomEvent.ConnectionStateChanged, onConnectionStateChange);
    return () => {
      room.off(RoomEvent.ConnectionStateChanged, onConnectionStateChange);
    };
  }, [room]);

  // Disconnect reason
  useEffect(() => {
    const onDisconnected = (disconnectReason) => {
      switch (disconnectReason) {
        case DisconnectReason.CLIENT_INITIATED:
          if (props.isMeetingFinished) window.location.href = "/meeting/ended";
          else window.location.href = "/meeting/left";
          break;
        case DisconnectReason.ROOM_DELETED:
          window.location.href = "/meeting/ended";
          break;
        case DisconnectReason.PARTICIPANT_REMOVED:
          window.location.href = "/meeting/removed";
          break;
        case DisconnectReason.UNKNOWN_REASON:
          window.location.href = "/meeting/left";
          break;
        default:
          window.location.href = "/meeting/left";
      }
    };
    room.on(RoomEvent.Disconnected, onDisconnected);
    return () => {
      room.off(RoomEvent.Disconnected, onDisconnected);
    };
  }, [room]);

  // Track muted/unmuted
  useEffect(() => {
    if (!room) return;
    const onRefresh = () => {
      setRefresh((prev) => prev + 1);
    };
    room.on(RoomEvent.TrackMuted, onRefresh);
    room.on(RoomEvent.TrackUnmuted, onRefresh);
    return () => {
      room.off(RoomEvent.TrackMuted, onRefresh);
      room.off(RoomEvent.TrackUnmuted, onRefresh);
    };
  }, [room]);

  const fetchBreakoutRoomConfig = async () => {
    try {
      const response = await BreakoutRoomService.getBreakoutRoomConfig(
          props.meetingDetails?.room_uid,
          coHostToken,
          room?.localParticipant?.participantInfo
      );
      if (response?.success === 0) {
        console.log("Error getting breakout room config", response);
        return;
      }
      return response;
    } catch (error) {
      setToastNotification(error.message);
      setToastStatus("error");
      setShowToast(true);
      // console.log("Error getting breakout room config", error);
    }
  };

  // Initial Use Effect
  const breakoutRoomEndTime = useRef(null);

  useEffect(() => {
    const fetchSipConfig = async () => {
      if (room?.state === "connected") {
        try {
          const sipResponse = await VideoConferenceService.sipConnection(
              props.id,
              room?.localParticipant?.participantInfo
          );
          if (sipResponse?.success === 1) {
            setSipData(sipResponse?.data);
          }
        } catch (error) {
          setToastNotification(error.message);
          setToastStatus("error");
          setShowToast(true);
          // console.log(error);
        }
      }
    };
    const fetchConfig = async () => {
      // set the room name
      if (room?.state === "connected") {
        if (
            parseMetadata(room?.localParticipant?.metadata)?.role_name ===
            "moderator"
        ) {
          await fetchBreakoutRoomDetails();
        }
        const roomNumber = room?.roomInfo?.name.split("__BR")[1];
        const roomkey = roomNumber || "0";
        const roomName = roomNumber ? `Breakout Room ${roomkey}` : "Main Room";
        setCurrentRoomName(roomName);
        setIsBreakoutRoom(!!roomNumber);
        if (
            parseMetadata(room?.localParticipant?.metadata)?.role_name === "cohost"
        ) {
          const tokenData = getLocalStorage(constants.CO_HOST_TOKEN);
          if (
              tokenData &&
              tokenData.token &&
              tokenData.meetingId === props.id
          ) {
            setCoHostToken(tokenData.token);
            setIsCoHost(true);
          }
        }
        if (roomkey !== "0") {
          const response = await fetchBreakoutRoomConfig();
          if (response?.success === 1) {
            response?.data?.forEach((config) => {
              if (config?.room_uid === room?.roomInfo?.name) {
                const startTime = moment(config?.start_datetime).local();

                const currentTime = moment();

                const differenceInSeconds = currentTime.diff(
                    startTime,
                    "seconds"
                );
                setBreakoutRoomDuration(config?.auto_timeout);
                const timeoutSeconds = config?.auto_timeout * 60;
                const leftTimeout = timeoutSeconds - differenceInSeconds;

                if (leftTimeout > 0) {
                  setEndBreakoutRoomTimer(leftTimeout);
                  breakoutRoomEndTime.current = moment().add(
                      leftTimeout,
                      "seconds"
                  );
                } else {
                  // setBreakoutRoomDuration(0);
                  breakoutRoomEndTime.current = 0;
                  setEndBreakoutRoomTimer(0);
                }
              }
            });
          }
        } else {
          const response = await fetchBreakoutRoomConfig();
          if (response?.success === 1) {
            setBreakoutRoomDuration(response?.data[0]?.auto_timeout || 5);
            if (
                response?.data[0]?.auto_timeout &&
                Object.keys(breakoutRooms).length > 1
            ) {
              setIsBreakoutRoomCnfigSet(true);
            }
          }
        }
      }
    };

    const requestWhiteboardData = async () => {
      if (room?.state === "connected") {
        const response = await WhiteboardService.getWhiteboardDetail(
            props.meetingDetails.id,
            room?.localParticipant?.participantInfo
        );
        if (response.success !== 0) {
          const remoteParticipants = Array.from(
              room?.remoteParticipants.values()
          );
          if (
              response.data[0]?.status === "open" &&
              remoteParticipants.length > 0
          ) {
            const encoder = new TextEncoder();
            const data = encoder.encode(
                JSON.stringify({
                  action: DataReceivedEvent.WHITEBOARD_DATA_REQUEST,
                })
            );
            const participant = remoteParticipants[0]?.identity;
            room?.localParticipant.publishData(data, {
              reliable: true,
              destinationIdentities: [participant],
            });
          }
          setWhiteboardId(() => response.data[0]?.id);
          setIsWhiteboardOpen(response.data[0]?.status === "open");
        }
      }
    };

    if (room?.state === "connected" && !props.isHost && !isCoHost) {
      setIsLiveCaptionsDrawerOpen(
          props.meetingDetails?.transcription_detail?.transcription_enable
      );
    }

    const requestLiveCaptionsStateData = () => {
      if (room?.state === "connected") {
        const encoder = new TextEncoder();
        const remoteParticipants = Array.from(
            room?.remoteParticipants.values()
        );
        const participant = remoteParticipants.find((part) => {
          try {
            const metadata = part?.metadata ? parseMetadata(part.metadata) : null;
            return metadata?.role_name === "participant";
          } catch (error) {
            setToastNotification(error.message);
            setToastStatus("error");
            setShowToast(true);
            // console.error("Invalid metadata JSON:", part?.metadata, error);
            return false;
          }
        });
        if (participant) {
          const data = encoder.encode(
              JSON.stringify({
                action: DataReceivedEvent.REQUEST_LIVECAPTION_DRAWER_STATE,
              })
          );
          room?.localParticipant.publishData(data, {
            reliable: true,
            destinationIdentities: [participant?.identity],
          });
        }
      }
    };

    const checkForAutoRecording = async () => {
      if (
          room?.state === "connected" &&
          props.meetingDetails?.meeting_config?.auto_start_recording === 1 &&
          props.meetingDetails?.meeting_config?.recording_force_stopped === 0 &&
          !props.meetingDetails?.is_recording_active &&
          (parseMetadata(room?.localParticipant?.metadata)?.role_name ===
              "moderator" ||
              parseMetadata(room?.localParticipant?.metadata)?.role_name === "cohost")
      ) {
        await new Promise((resolve) => setTimeout(resolve, 6000)); // added delay for 6sec untill room setup
        const response = await SettingsMenuServices.startCloudRecording(
            props.id,
            coHostToken,
            room?.localParticipant?.participantInfo
        );
        if (response.success === 1) setShowRecording(true);
        else setShowRecording(false);
      }
    };

    const checkForCohost = async () => {
      try {
        if (room?.state !== "connected") return;
        const cohostDetail = getLocalStorage(constants.CO_HOST_TOKEN);
        if (
            cohostDetail &&
            cohostDetail.token &&
            cohostDetail.meetingId === props.id &&
            parseMetadata(room?.localParticipant?.metadata)?.role_name === "cohost"
        ) {
          setCoHostToken(coHostToken?.token);
          setIsCoHost(true);
        }
      } catch (error) {
        setToastNotification(error.message);
        setToastStatus("error");
        setShowToast(true);
        // console.log("Error: ",error)
      }
    };

    const checkRecordingIsActive = () => {
      if (room?.state === "connected" && props.meetingDetails?.is_recording_active) {
        new Audio(recordingStart).play();
        setToastNotification("This meeting is being recorded...");
        setToastStatus("info");
        setShowToast(true);
        setShowRecording(true);
      }
    }

    checkRecordingIsActive();
    checkForCohost();
    checkForAutoRecording();
    requestLiveCaptionsStateData();
    requestWhiteboardData();
    fetchConfig();
    fetchSipConfig();
  }, [room?.state]);

  // Recording status change
  useEffect(() => {
    if (!room) return;
    const onRecordingStatusChange = (isRecording) => {
      // console.log("Recording status changed", isRecording);
      if (isRecording) {
        new Audio(recordingStart).play();
        setToastNotification("Recording started...");
        setToastStatus("info");
        setShowToast(true);
        setShowRecording(true);
        setIsRecordingLoading(false);
      } else {
        new Audio(recordingStop).play();
        setToastNotification("Recording stopped...");
        setToastStatus("info");
        setShowToast(true);
        setShowRecording(false);
        setIsRecordingLoading(false);
      }
    };
    room.on(RoomEvent.RecordingStatusChanged, onRecordingStatusChange);
    return () => {
      room.off(RoomEvent.RecordingStatusChanged, onRecordingStatusChange);
    };
  }, [room]);

  // Timer to hide toast notification after 5 seconds
  useEffect(() => {
    if (!room) return;

    const timer = setTimeout(() => {
      if (showToast) setShowToast(false);
    }, 5000);

    // Clear the timeout if the component unmounts or if showToast changes
    return () => clearTimeout(timer);
  }, [showToast]);

  // Timer to end breakout room
  useEffect(() => {
    if (!room) return;
    if (!isBreakoutRoom || breakoutRoomEndTime.current === null) return;
    const intervalId = setInterval(async () => {
      const currentTime = moment();
      setEndBreakoutRoomTimer(
          breakoutRoomEndTime.current.diff(currentTime, "seconds")
      );
      if (endBreakoutRoomTimer <= 0) {
        const response = await BreakoutRoomService.getBreakoutRoomToken(
            room.localParticipant,
            `${props.meetingDetails?.room_uid}`
        );
        if (response?.success === 0) {
          console.log("Error getting breakout room token", response);
          return;
        }
        if (response?.data?.access_token) {
          setIsMovingToRoom(true);
          setMovingRoomToken(response?.data?.access_token?.token);
          if (
              parseMetadata(room?.localParticipant?.metadata)?.role_name ===
              "moderator"
          ) {
            await fetchBreakoutRoomDetails();
          }
        }
        breakoutRoomEndTime.current = null;
      }
    }, 1000);
    return () => clearInterval(intervalId);
  }, [room, isBreakoutRoom, endBreakoutRoomTimer, isCoHost]);

  const formatEndBreakoutRoomTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, "0")}:${remainingSeconds
        .toString()
        .padStart(2, "0")}`;
  };

  const [showChatDrawer, setShowChatDrawer] = useState(false);

  // Alert on back button press
  const [isAlertVisible, setIsAlertVisible] = useState(false);

  const onBackButtonEvent = (e) => {
    e.preventDefault(); // Prevent default back navigation
    if (!isAlertVisible) {
      e.preventDefault(); // Prevent default back navigation
      setIsAlertVisible(true); // Show alert confirmation
    } else {
      e.preventDefault(); // Prevent default back navigation
      setIsAlertVisible(false); // Hide alert confirmation
    }
  };
  const handleConfirm = () => {
    setIsAlertVisible(false);
    room.disconnect();
    if (isElectronApp) {
      window?.electronAPI?.ipcRenderer?.send("stop-annotation");
    }
  };
  const handleCancel = () => {
    setIsAlertVisible(false);
    window.history.pushState(null, null, window.location.pathname);
  };
  useEffect(() => {
    window.addEventListener("popstate", onBackButtonEvent);

    window.history.pushState(null, null, window.location.pathname);
    return () => {
      window.removeEventListener("popstate", onBackButtonEvent);
    };
  }, []);

  const [targetLangChangedFor, setTargetLangChangedFor] = useState(0);
  // Storing the final translated captions
  useEffect(() => {
    // Check if translationDetails?.target_lang is defined and changes
    if (translationDetails?.target_lang && targetLangChangedFor === 0) {
      setTargetLangChangedFor(1);
      setFinalTranslatedCaptions([...finalCaptions]);
    } else if (translationDetails?.target_lang && targetLangChangedFor !== 0) {
      setFinalTranslatedCaptions((prevCaptions) => [...prevCaptions]);
    }
  }, [translationDetails?.target_lang]);

  useEffect(() => {
    if (room) {
      if (focusTrack) {
        setIsFocusTrackEnabled(true);
      } else {
        setIsFocusTrackEnabled(false);
      }
    }
  }, [room, focusTrack]);

  useEffect(() => {
    if(props.isHost || isCoHost){

      if(liveCaptionsObject.isLanguageSelected){
        setLiveCapTourState(prev => ({
          ...prev,
          run: true,
          steps: [
            {
              content: 'This is the Live Captions drawer.',
              target: '.live-captions',
              title: 'Live Captions Drawer',
              disableBeacon: true,
              spotlightClicks: false,
              placement: 'left',
            },
            {
              content: 'Here you will be able to see the captions in the selected language.',
              target: '.live-cap',
              title: 'Live Captions',
              spotlightClicks: false,
              placement: 'left',
            },
          ]
        }));
      } else {
        setLiveCapTourState(prev => ({
          ...prev,
          run: true,
          steps: [
            {
              content: 'This is the Live Captions drawer.',
              target: '.live-captions',
              title: 'Live Captions Drawer',
              disableBeacon: true,
              spotlightClicks: false,
              placement: 'left',
            },
            {
              content: 'This is the Search Bar where you can search for your preferred language for the captions.',
              target: '.language-search-box',
              title: 'Search Language',
              spotlightClicks: true,
              placement: 'left',
            },
            {
              content: 'Select the language you want to see the captions in.',
              target: '.language-options',
              title: 'Select Language',
              spotlightClicks: true,
              placement: 'left',
            },
            {
              content: 'Here you will be able to see the captions in the selected language.',
              target: '.live-cap',
              title: 'Live Captions',
              spotlightClicks: false,
              placement: 'left',
            },
          ],
        }));
      }
    } else {
      // If the user is not a host or co-host
      setLiveCapTourState(prev => ({
        ...prev,
        run: true,
        steps: [
          {
            content: 'This is the Live Captions drawer.',
            target: '.live-captions',
            title: 'Live Captions Drawer',
            disableBeacon: true,
            spotlightClicks: false,
            placement: 'left',
          },
          {
            content: 'By clicking here you will be able to select your preferred language for live transcription.',
            target: '.live-cap-settings-icon',
            title: 'Live Transcription Language',
            disableBeacon: true,
            spotlightClicks: true,
            placement: 'left',
          },
          {
            content: 'Here you can search for your preferred language to see the captions in.',
            target: '.language-search-box',
            title: 'Live Transcription Language Search',
            disableBeacon: true,
            spotlightClicks: true,
            placement: 'left',
            scrollToSteps: true,
            event: 'scroll',
          },
          {
            content: 'Select the language you want to see the captions in.',
            target: '.language-options',
            title: 'Select Language',
            spotlightClicks: true,
            disableBeacon: true,
            placement: 'left',
          },
          {
            content: 'Here you will be able to see the captions in the selected language.',
            target: '.live-cap',
            title: 'Live Captions',
            spotlightClicks: false,
            disableBeacon: true,
            placement: 'left',
          },
        ]
      }));
    }
  }, [liveCapTourState.sidebarOpen]);

  const handleJoyrideCallback = (data) => {
    const { action, index, status, type } = data;
    if (status === 'finished' || status === 'skipped') {
      setLiveCapTourState(prev => ({ ...prev, run: false, stepIndex: 0 }));
    } else if (type === 'step:after' || type === 'target_not_found') {
      const nextStepIndex = index + (action === 'prev' ? -1 : 1);
      setLiveCapTourState(prev => ({ ...prev, stepIndex: nextStepIndex }));
    }
  };

  useEffect(() => {
    if(isLiveCaptionsDrawerOpen) {
      setLiveCapTourState(prev => ({
        ...prev,
        run: true,
        sidebarOpen: true,
      }));
    } else {
      setLiveCapTourState(prev => ({
        ...prev,
        run: false,
        sidebarOpen: false,
      }));
    }
  }, [isLiveCaptionsDrawerOpen]);

  // useEffect(() => {
  //   if (liveCapTourState.stepIndex === 3) {
  //     setLiveCapTourState(prev => ({
  //       ...prev,
  //       run: false,
  //       stepIndex: 3,
  //     })); // Stop tour temporarily
  //     const waitForElement = setInterval(() => {
  //       const liveCapEl = document.querySelector(".live-cap");
  //       if (liveCapEl) {
  //         clearInterval(waitForElement);

  //         // 🎯 Scroll to the element before showing the step
  //         liveCapEl.scrollIntoView({ behavior: "smooth", block: "center" });

  //         // ⏳ Small delay to ensure smooth transition
  //         setTimeout(() => {
  //           setLiveCapTourState(prev => ({
  //             ...prev,
  //             run: true,
  //           }));  // Restart after ensuring visibility
  //         }, 500);
  //       }
  //     }, 300);
  //   }
  //   console.log("stepIndex", liveCapTourState.stepIndex);
  // }, [liveCapTourState.stepIndex]);


  return isPipWindow ? (
      <PipLayout
          layoutContext={layoutContext}
          focusTrack={focusTrack}
          carouselTracks={carouselTracks}
          // setIsPipWindow={setIsPipWindow}
          // isPipWindow={isPipWindow}
          isForceMuteAll={isForceMuteAll}
          isCoHost={isCoHost}
          isHost={props.isHost}
          isForceVideoOffAll={isForceVideoOffAll}
          isScreenShareEnabled={isScreenShareEnabled}
          // setIsScreenShareEnabled={setIsScreenShareEnabled}
          onScreenShareChange={onScreenShareChange}
          screenShareMode={screenShareMode}
          maxWidth={props.maxWidth}
          maxHeight={props.maxHeight}
      />
  ) : (
      <div
          className={`lk-video-conference ${
              isElectron() && "lk-video-conference-electron"
          } ${isMobileBrowser() ? "mobile-video-conference" : ""}`}
          {...props}
          style={{ height: "100svh" }}
      >
        {isMobileBrowser() && (
          <div className="mobile-video-conference-upper-controlbar">
              <DaakiaLogo/>

              <MediaDeviceMenu
  kind="videoinput"
  onActiveDeviceChange={(_kind, deviceId) => {
    // Set the correct video device ID
    setDeviceIdVideo(deviceId ?? "");

    // Check if the device is a back camera and handle mirroring
    if (deviceId) {
      navigator.mediaDevices.enumerateDevices().then(devices => {
        const selectedDevice = devices.find(device =>
          device.kind === "videoinput" && device.deviceId === deviceId
        );

        if (selectedDevice) {
          // Check if it's a back camera (more comprehensive check)
          const isBackCamera = selectedDevice.label.toLowerCase().includes('back') ||
                              selectedDevice.label.toLowerCase().includes('rear') ||
                              selectedDevice.label.toLowerCase().includes('environment');

          if (isBackCamera) {
            // Store current mirroring preference before switching to back camera
            // (only store if we haven't stored it already)
            if (frontCameraMirroringPreference === undefined) {
              setFrontCameraMirroringPreference(isSelfVideoMirrored);
            }
            // Force mirroring OFF for back cameras
            setIsSelfVideoMirrored(false);
          } else {
            // Front camera selected
            if (frontCameraMirroringPreference !== undefined) {
              // Restore the user's original front camera preference
              setIsSelfVideoMirrored(frontCameraMirroringPreference);
              // Clear the stored preference since we've restored it
              setFrontCameraMirroringPreference(undefined);
            }
            // If no stored preference, don't change anything (preserve current setting)
          }
        }
      }).catch(error => {
        console.error('Error enumerating devices:', error);
        // Fallback: assume front camera, set mirroring to true
        setIsSelfVideoMirrored(true);
      });
    }
  }}
>
  <div className="camera-select-icon">
    <VideoSwitch />
  </div>
</MediaDeviceMenu>


          </div>
        )}
        {isMobileBrowser()
            ? isAlertVisible && (
            <div className="back-alert-mobile">
              <p>Do you want to exit the meeting?</p>
              <div className="back-alert-confirm">
                <Button type="primary" onClick={handleConfirm}>
                  Yes
                </Button>
                <Button onClick={handleCancel}>No</Button>
              </div>
            </div>
        )
            : isAlertVisible && (
            <div className="back-alert-web">
              <div className="back-alert-web-box">
                <p>Do you want to exit the meeting?</p>
                <div className="back-alert-confirm">
                  <Button type="primary" onClick={handleConfirm}>
                    Yes
                  </Button>
                  <Button onClick={handleCancel}>No</Button>
                </div>
              </div>
            </div>
          )}
      {isBreakoutRoom && (
        <div className="br-timer">
          {formatEndBreakoutRoomTime(endBreakoutRoomTimer)}
        </div>
      )}
      {isWeb() && (
        <LayoutContextProvider
          value={layoutContext}
          onWidgetChange={widgetUpdate}
        >
          <div className="lk-video-conference-inner">
            {!isScreenShareEnabled &&
              !isFocusTrackEnabled &&
              !isWhiteboardOpen && (
                <DaakiaLogo
                  className={`daakia-logo ${
                    isElectron() && "daakia-logo-electron"
                  } ${isMobileBrowser() && "hidden"}`}
                />
              )}
            {!focusTrack ? (
              isWhiteboardOpen ? (
                <div className="lk-focus-layout-wrapper whiteboard-focus">
                  <CarouselLayout tracks={carouselTracks}>
                    <TrackRefContext.Consumer>
                      {(trackRef) => (
                        <ParticipantTile
                          trackRef={trackRef}
                          showEmojiReaction={showEmojiReaction}
                          setShowEmojiReaction={setShowEmojiReaction}
                          showRaiseHand={showRaiseHand}
                          remoteRaisedHands={remoteRaisedHands}
                          remoteEmojiReactions={remoteEmojiReactions}
                          setRemoteEmojiReactions={setRemoteEmojiReactions}
                        />
                      )}
                    </TrackRefContext.Consumer>
                  </CarouselLayout>
                  <Whiteboard
                    room={room}
                    isWhiteboardOpen={isWhiteboardOpen}
                    setIsWhiteboardOpen={setIsWhiteboardOpen}
                    // socket={socket}
                    token={props.token}
                    isHost={props.isHost}
                    allowLiveCollabWhiteBoard={allowLiveCollabWhiteBoard}
                    isCoHost={isCoHost}
                    whiteboardData={whiteboardData}
                    setWhiteboardData={setWhiteboardData}
                    socket={socket}
                    isSocketConnected={isSocketConnected}
                    isExitWhiteboardModalOpen={isExitWhiteboardModalOpen}
                    setIsExitWhiteboardModalOpen={setIsExitWhiteboardModalOpen}
                    whiteboardSceneData={whiteboardSceneData}
                    setWhiteboardSceneData={setWhiteboardSceneData}
                    meetingId={props.id}
                    whiteBoardId={whiteboardId}
                    meetingDetails={props.meetingDetails}
                    setToastNotification={setToastNotification}
                    setToastStatus={setToastStatus}
                    setShowToast={setShowToast}
                  />
                  {/* Chat Drawer */}
                  {meetingFeatures?.conference_chat === 1 &&
                    (isMobileBrowser() ? (
                      <ChatsDrawer
                        messageFormatter={formatChatMessageLinks}
                        privatechatparticipants={privatechatparticipants}
                        setprivatechatparticipants={setPrivateChatParticipants}
                        selectedprivatechatparticipant={
                          selectedPrivateChatParticipant
                        }
                        setselectedprivatechatparticipant={
                          setSelectedPrivateChatParticipant
                        }
                        showPrivateChat={showPrivateChat}
                        setShowPrivateChat={setShowPrivateChat}
                        localparticipant={room.localParticipant}
                        privatechatmessages={privatechatmessages}
                        setprivatechatmessages={setPrivateChatMessages}
                        newmessagerender={newMessageRender}
                        show={showChatDrawer}
                        setShow={setShowChatDrawer}
                        setPrivateChatUnreadMessagesCount={
                          setPrivateChatUnreadMessagesCount
                        }
                        privateChatUnreadMessagesCount={
                          privateChatUnreadMessagesCount
                        }
                        setPublicChatUnreadMessagesCount={
                          setPublicChatUnreadMessagesCount
                        }
                        publicChatUnreadMessagesCount={
                          publicChatUnreadMessagesCount
                        }
                        canDownloadChatAttachment={canDownloadChatAttachment}
                        chatMessages={publicChatMessages}
                        setpublicchatmessage={setPublicChatMessages}
                        setToastNotification={setToastNotification}
                        setToastStatus={setToastStatus}
                        setShowToast={setShowToast}
                      />
                    ) : (
                      <ChatsDrawer
                        messageFormatter={formatChatMessageLinks}
                        privatechatparticipants={privatechatparticipants}
                        setprivatechatparticipants={setPrivateChatParticipants}
                        selectedprivatechatparticipant={
                          selectedPrivateChatParticipant
                        }
                        setselectedprivatechatparticipant={
                          setSelectedPrivateChatParticipant
                        }
                        showPrivateChat={showPrivateChat}
                        setShowPrivateChat={setShowPrivateChat}
                        localparticipant={room.localParticipant}
                        privatechatmessages={privatechatmessages}
                        setprivatechatmessages={setPrivateChatMessages}
                        newmessagerender={newMessageRender}
                        show={showChatDrawer}
                        setShow={setShowChatDrawer}
                        setPrivateChatUnreadMessagesCount={
                          setPrivateChatUnreadMessagesCount
                        }
                        privateChatUnreadMessagesCount={
                          privateChatUnreadMessagesCount
                        }
                        setPublicChatUnreadMessagesCount={
                          setPublicChatUnreadMessagesCount
                        }
                        publicChatUnreadMessagesCount={
                          publicChatUnreadMessagesCount
                        }
                        canDownloadChatAttachment={canDownloadChatAttachment}
                        chatMessages={publicChatMessages}
                        setpublicchatmessage={setPublicChatMessages}
                        isWhiteboardOpen={isWhiteboardOpen}
                        setToastNotification={setToastNotification}
                        setToastStatus={setToastStatus}
                        setShowToast={setShowToast}
                      />
                    ))}
                  {/* Live Captions */}
                  {meetingFeatures?.voice_transcription === 1 &&
                    isLiveCaptionsDrawerOpen && (
                      <LiveCaptionsDrawer
                        isLiveCaptionsDrawerOpen={isLiveCaptionsDrawerOpen}
                        setIsLiveCaptionsDrawerOpen={
                          setIsLiveCaptionsDrawerOpen
                        }
                        remoteParticipants={room.remoteParticipants}
                        localParticipant={room.localParticipant}
                        liveCaptionData={liveCaptionData}
                        meetingDetails={props.meetingDetails}
                        livecaptionsobject={liveCaptionsObject}
                        setlivecaptionsobject={setLiveCaptionsObject}
                        setfinalcaptions={setFinalCaptions}
                        finalcaptions={finalCaptions}
                        id={props.id}
                        isWhiteboardOpen={isWhiteboardOpen}
                        setDrawerState={setDrawerState}
                        translationDetails={translationDetails}
                        setTranslationDetails={setTranslationDetails}
                        finalTranslatedCaptions={finalTranslatedCaptions}
                        setFinalTranslatedCaptions={setFinalTranslatedCaptions}
                        meetingFeatures={meetingFeatures}
                        coHostToken={coHostToken}
                        setToastNotification={setToastNotification}
                        setToastStatus={setToastStatus}
                        setShowToast={setShowToast}
                        setLiveCapTourState={setLiveCapTourState}
                      />
                    )}
                  {/* Participant Drawer */}
                  {showParticipantsList &&
                    connected &&
                    (isMobileBrowser() ? (
                      <div className="side-drawer-mobile">
                        <ParticipantList
                          remoteParticipants={room.remoteParticipants}
                          localParticipant={room.localParticipant}
                          showParticipantsList={showParticipantsList}
                          setShowParticipantsList={setShowParticipantsList}
                          id={props.id}
                          isHost={props.isHost}
                          layoutContext={layoutContext}
                          isCoHost={isCoHost}
                          lobbyParticipants={lobbyParticipants}
                          setLobbyParticipants={setLobbyParticipants}
                          setRemoteRaisedHands={setRemoteRaisedHands}
                          setDrawerState={setDrawerState}
                          coHostToken={coHostToken}
                          setShowRaiseHand={setShowRaiseHand}
                          currentRoomName={currentRoomName}
                          setBreakoutRoomDuration={setBreakoutRoomDuration}
                          meetingDetails={props.meetingDetails}
                          breakoutRooms={breakoutRooms}
                          setBreakoutRooms={setBreakoutRooms}
                          setRoomKeyCounter={setRoomKeyCounter}
                          isBreakoutRoomCnfigSet={isBreakoutRoomCnfigSet}
                          setIsBreakoutRoomCnfigSet={setIsBreakoutRoomCnfigSet}
                          meetingFeatures={meetingFeatures}
                          setselectedprivatechatparticipant={
                            setSelectedPrivateChatParticipant
                          }
                          setprivatechatparticipants={
                            setPrivateChatParticipants
                          }
                          privatechatparticipants={privatechatparticipants}
                          setshowprivatechat={setShowPrivateChat}
                          forcemute={forceMute}
                          forcevideooff={forceVideoOff}
                          room={room}
                          isBreakoutRoom={isBreakoutRoom}
                          allowLiveCollabWhiteBoard={allowLiveCollabWhiteBoard}
                          isWhiteboardOpen={isWhiteboardOpen}
                          isPinned={isPinned}
                          setIsPinned={setIsPinned}
                          setToastNotification={setToastNotification}
                          setToastStatus={setToastStatus}
                          setShowToast={setShowToast}
                        />
                      </div>
                    ) : (
                      <ParticipantList
                        remoteParticipants={room.remoteParticipants}
                        localParticipant={room.localParticipant}
                        showParticipantsList={showParticipantsList}
                        setShowParticipantsList={setShowParticipantsList}
                        id={props.id}
                        isHost={props.isHost}
                        layoutContext={layoutContext}
                        isCoHost={isCoHost}
                        lobbyParticipants={lobbyParticipants}
                        setLobbyParticipants={setLobbyParticipants}
                        setRemoteRaisedHands={setRemoteRaisedHands}
                        coHostToken={coHostToken}
                        setDrawerState={setDrawerState}
                        breakoutRooms={breakoutRooms}
                        setShowRaiseHand={setShowRaiseHand}
                        currentRoomName={currentRoomName}
                        setToastNotification={setToastNotification}
                        setToastStatus={setToastStatus}
                        setShowToast={setShowToast}
                        setBreakoutRoomDuration={setBreakoutRoomDuration}
                        meetingDetails={props.meetingDetails}
                        setBreakoutRooms={setBreakoutRooms}
                        setRoomKeyCounter={setRoomKeyCounter}
                        isBreakoutRoomCnfigSet={isBreakoutRoomCnfigSet}
                        setIsBreakoutRoomCnfigSet={setIsBreakoutRoomCnfigSet}
                        meetingFeatures={meetingFeatures}
                        setselectedprivatechatparticipant={
                          setSelectedPrivateChatParticipant
                        }
                        setprivatechatparticipants={setPrivateChatParticipants}
                        privatechatparticipants={privatechatparticipants}
                        setshowprivatechat={setShowPrivateChat}
                        forcemute={forceMute}
                        forcevideooff={forceVideoOff}
                        room={room}
                        isBreakoutRoom={isBreakoutRoom}
                        allowLiveCollabWhiteBoard={allowLiveCollabWhiteBoard}
                        isWhiteboardOpen={isWhiteboardOpen}
                        isPinned={isPinned}
                        setIsPinned={setIsPinned}
                      />
                    ))}
                  {/* Host Controls */}
                  {showHostControl && (
                    <HostControlDrawer
                      showHostControl={showHostControl}
                      setShowHostControl={setShowHostControl}
                      room={room}
                      setForceVideoOff={setForceVideoOff}
                      forceVideoOff={forceVideoOff}
                      setForceMute={setForceMute}
                      forceMute={forceMute}
                      setCanDownloadChatAttachment={
                        setCanDownloadChatAttachment
                      }
                      canDownloadChatAttachment={canDownloadChatAttachment}
                      setAllowLiveCollabWhiteBoard={
                        setAllowLiveCollabWhiteBoard
                      }
                      allowLiveCollabWhiteBoard={allowLiveCollabWhiteBoard}
                      isWhiteboardOpen={isWhiteboardOpen}
                      setDrawerState={setDrawerState}
                    />
                  )}
                  {/* Breakout Rooms */}
                  {meetingFeatures?.breakout_room === 1 && showBreakoutRoom && (
                    <BreakoutRoomDrawer
                      showBreakoutRoom={showBreakoutRoom}
                      setShowBreakoutRoom={setShowBreakoutRoom}
                      remoteParticipants={room.remoteParticipants}
                      localParticipant={room.localParticipant}
                      breakoutRooms={breakoutRooms}
                      setBreakoutRooms={setBreakoutRooms}
                      id={props.id}
                      setRoomKeyCounter={setRoomKeyCounter}
                      roomKeyCounter={roomKeyCounter}
                      setToastNotification={setToastNotification}
                      setToastStatus={setToastStatus}
                      setShowToast={setShowToast}
                      breakoutRoomDuration={breakoutRoomDuration}
                      meetingDetails={props.meetingDetails}
                      coHostToken={coHostToken}
                      setDrawerState={setDrawerState}
                      setIsBreakoutRoomCnfigSet={setIsBreakoutRoomCnfigSet}
                      isWhiteboardOpen={isWhiteboardOpen}
                    />
                  )}
                  {/* Virtual Background Drawer */}
                  {isVBDrawerOpen && (
                    <VirtualBackgroundDrawer
                      isVBDrawerOpen={isVBDrawerOpen}
                      setIsVBDrawerOpen={setIsVBDrawerOpen}
                      room={room}
                      setToastNotification={setToastNotification}
                      setToastStatus={setToastStatus}
                      setShowToast={setShowToast}
                      isWhiteboardOpen={isWhiteboardOpen}
                      setDrawerState={setDrawerState}
                    />
                  )}
                  {/* Report Problem */}
                  {isRPDrawerOpen && (
                    <ReportProblemDrawer
                      isRPDrawerOpen={isRPDrawerOpen}
                      setIsRPDrawerOpen={setIsRPDrawerOpen}
                      id={props.id}
                      clientPreferedServerId={props.clientPreferedServerId}
                      isWhiteboardOpen={isWhiteboardOpen}
                      localParticipant={room?.localParticipant}
                      setDrawerState={setDrawerState}
                      setToastNotification={setToastNotification}
                      setToastStatus={setToastStatus}
                      setShowToast={setShowToast}
                    />
                  )}
                </div>
              ) : (
                <div className="lk-grid-layout-wrapper">
                  <GridLayout tracks={tracks}>
                    <TrackRefContext.Consumer>
                      {(trackRef) => (
                        <ParticipantTile
                          trackRef={trackRef}
                          showEmojiReaction={showEmojiReaction}
                          setShowEmojiReaction={setShowEmojiReaction}
                          showRaiseHand={showRaiseHand}
                          remoteRaisedHands={remoteRaisedHands}
                          remoteEmojiReactions={remoteEmojiReactions}
                          setRemoteEmojiReactions={setRemoteEmojiReactions}
                          isSelfVideoMirrored={isSelfVideoMirrored}
                          setIsSelfVideoMirrored={setIsSelfVideoMirrored}
                        />
                      )}
                    </TrackRefContext.Consumer>
                  </GridLayout>
                  {/* Chat */}
                  {meetingFeatures?.conference_chat === 1 &&
                    (isMobileBrowser() ? (
                      <ChatsDrawer
                        messageFormatter={formatChatMessageLinks}
                        privatechatparticipants={privatechatparticipants}
                        setprivatechatparticipants={setPrivateChatParticipants}
                        selectedprivatechatparticipant={
                          selectedPrivateChatParticipant
                        }
                        setselectedprivatechatparticipant={
                          setSelectedPrivateChatParticipant
                        }
                        showPrivateChat={showPrivateChat}
                        setShowPrivateChat={setShowPrivateChat}
                        localparticipant={room.localParticipant}
                        privatechatmessages={privatechatmessages}
                        setprivatechatmessages={setPrivateChatMessages}
                        newmessagerender={newMessageRender}
                        show={showChatDrawer}
                        setShow={setShowChatDrawer}
                        setPrivateChatUnreadMessagesCount={
                          setPrivateChatUnreadMessagesCount
                        }
                        privateChatUnreadMessagesCount={
                          privateChatUnreadMessagesCount
                        }
                        setPublicChatUnreadMessagesCount={
                          setPublicChatUnreadMessagesCount
                        }
                        publicChatUnreadMessagesCount={
                          publicChatUnreadMessagesCount
                        }
                        canDownloadChatAttachment={canDownloadChatAttachment}
                        chatMessages={publicChatMessages}
                        setpublicchatmessage={setPublicChatMessages}
                        setToastNotification={setToastNotification}
                        setToastStatus={setToastStatus}
                        setShowToast={setShowToast}
                      />
                    ) : (
                      <ChatsDrawer
                        messageFormatter={formatChatMessageLinks}
                        privatechatparticipants={privatechatparticipants}
                        setprivatechatparticipants={setPrivateChatParticipants}
                        selectedprivatechatparticipant={
                          selectedPrivateChatParticipant
                        }
                        setselectedprivatechatparticipant={
                          setSelectedPrivateChatParticipant
                        }
                        showPrivateChat={showPrivateChat}
                        setShowPrivateChat={setShowPrivateChat}
                        localparticipant={room.localParticipant}
                        privatechatmessages={privatechatmessages}
                        setprivatechatmessages={setPrivateChatMessages}
                        newmessagerender={newMessageRender}
                        show={showChatDrawer}
                        setShow={setShowChatDrawer}
                        setPrivateChatUnreadMessagesCount={
                          setPrivateChatUnreadMessagesCount
                        }
                        privateChatUnreadMessagesCount={
                          privateChatUnreadMessagesCount
                        }
                        setPublicChatUnreadMessagesCount={
                          setPublicChatUnreadMessagesCount
                        }
                        publicChatUnreadMessagesCount={
                          publicChatUnreadMessagesCount
                        }
                        canDownloadChatAttachment={canDownloadChatAttachment}
                        chatMessages={publicChatMessages}
                        setpublicchatmessage={setPublicChatMessages}
                        setToastNotification={setToastNotification}
                        setToastStatus={setToastStatus}
                        setShowToast={setShowToast}
                      />
                    ))}
                  {/* {meetingFeatures?.conference_chat === 1 &&
                    (isMobileBrowser() ? (
                      <div
                        className={`lk-chat ${
                          widgetState.showChat && isMobileBrowser()
                            ? "show-chat-mobile"
                            : ""
                        }`}
                        style={{
                          display: widgetState.showChat ? "grid" : "none",
                        }}
                      >
                        <Chat messageFormatter={formatChatMessageLinks} />
                      </div>
                    ) : (
                      <div
                        className={`lk-chat ${
                          widgetState.showChat ? "lk-show-chat" : ""
                        }`}
                        style={{
                          display: widgetState.showChat ? "flex" : "none",
                        }}
                      >
                        <Chat messageFormatter={formatChatMessageLinks} />
                      </div>
                    ))} */}
                  {/* Live Captions */}
                  {meetingFeatures?.voice_transcription === 1 &&
                    isLiveCaptionsDrawerOpen && (
                      <LiveCaptionsDrawer
                        isLiveCaptionsDrawerOpen={isLiveCaptionsDrawerOpen}
                        setIsLiveCaptionsDrawerOpen={
                          setIsLiveCaptionsDrawerOpen
                        }
                        remoteParticipants={room.remoteParticipants}
                        localParticipant={room.localParticipant}
                        liveCaptionData={liveCaptionData}
                        meetingDetails={props.meetingDetails}
                        livecaptionsobject={liveCaptionsObject}
                        setlivecaptionsobject={setLiveCaptionsObject}
                        setfinalcaptions={setFinalCaptions}
                        finalcaptions={finalCaptions}
                        id={props.id}
                        isWhiteboardOpen={isWhiteboardOpen}
                        setDrawerState={setDrawerState}
                        translationDetails={translationDetails}
                        setTranslationDetails={setTranslationDetails}
                        finalTranslatedCaptions={finalTranslatedCaptions}
                        setFinalTranslatedCaptions={setFinalTranslatedCaptions}
                        meetingFeatures={meetingFeatures}
                        coHostToken={coHostToken}
                        setToastNotification={setToastNotification}
                        setToastStatus={setToastStatus}
                        setShowToast={setShowToast}
                        setLiveCapTourState={setLiveCapTourState}
                      />
                    )}
                  {/* Participant Drawer */}
                  {showParticipantsList &&
                    connected &&
                    (isMobileBrowser() ? (
                      <div className="side-drawer-mobile">
                        <ParticipantList
                          remoteParticipants={room.remoteParticipants}
                          localParticipant={room.localParticipant}
                          showParticipantsList={showParticipantsList}
                          setShowParticipantsList={setShowParticipantsList}
                          id={props.id}
                          isHost={props.isHost}
                          layoutContext={layoutContext}
                          isCoHost={isCoHost}
                          lobbyParticipants={lobbyParticipants}
                          setLobbyParticipants={setLobbyParticipants}
                          setRemoteRaisedHands={setRemoteRaisedHands}
                          setDrawerState={setDrawerState}
                          coHostToken={coHostToken}
                          setShowRaiseHand={setShowRaiseHand}
                          currentRoomName={currentRoomName}
                          setToastNotification={setToastNotification}
                          setToastStatus={setToastStatus}
                          setShowToast={setShowToast}
                          setBreakoutRoomDuration={setBreakoutRoomDuration}
                          meetingDetails={props.meetingDetails}
                          breakoutRooms={breakoutRooms}
                          setBreakoutRooms={setBreakoutRooms}
                          setRoomKeyCounter={setRoomKeyCounter}
                          isBreakoutRoomCnfigSet={isBreakoutRoomCnfigSet}
                          setIsBreakoutRoomCnfigSet={setIsBreakoutRoomCnfigSet}
                          meetingFeatures={meetingFeatures}
                          setselectedprivatechatparticipant={
                            setSelectedPrivateChatParticipant
                          }
                          setprivatechatparticipants={
                            setPrivateChatParticipants
                          }
                          privatechatparticipants={privatechatparticipants}
                          setshowprivatechat={setShowPrivateChat}
                          forcemute={forceMute}
                          forcevideooff={forceVideoOff}
                          room={room}
                          isBreakoutRoom={isBreakoutRoom}
                          allowLiveCollabWhiteBoard={allowLiveCollabWhiteBoard}
                          isWhiteboardOpen={isWhiteboardOpen}
                          isPinned={isPinned}
                          setIsPinned={setIsPinned}
                        />
                      </div>
                    ) : (
                      <ParticipantList
                        remoteParticipants={room.remoteParticipants}
                        localParticipant={room.localParticipant}
                        showParticipantsList={showParticipantsList}
                        setShowParticipantsList={setShowParticipantsList}
                        id={props.id}
                        isHost={props.isHost}
                        layoutContext={layoutContext}
                        isCoHost={isCoHost}
                        lobbyParticipants={lobbyParticipants}
                        setLobbyParticipants={setLobbyParticipants}
                        setRemoteRaisedHands={setRemoteRaisedHands}
                        coHostToken={coHostToken}
                        setDrawerState={setDrawerState}
                        breakoutRooms={breakoutRooms}
                        setShowRaiseHand={setShowRaiseHand}
                        currentRoomName={currentRoomName}
                        setToastNotification={setToastNotification}
                        setToastStatus={setToastStatus}
                        setShowToast={setShowToast}
                        setBreakoutRoomDuration={setBreakoutRoomDuration}
                        meetingDetails={props.meetingDetails}
                        setBreakoutRooms={setBreakoutRooms}
                        setRoomKeyCounter={setRoomKeyCounter}
                        isBreakoutRoomCnfigSet={isBreakoutRoomCnfigSet}
                        setIsBreakoutRoomCnfigSet={setIsBreakoutRoomCnfigSet}
                        meetingFeatures={meetingFeatures}
                        setselectedprivatechatparticipant={
                          setSelectedPrivateChatParticipant
                        }
                        setprivatechatparticipants={setPrivateChatParticipants}
                        privatechatparticipants={privatechatparticipants}
                        setshowprivatechat={setShowPrivateChat}
                        forcemute={forceMute}
                        forcevideooff={forceVideoOff}
                        room={room}
                        isBreakoutRoom={isBreakoutRoom}
                        allowLiveCollabWhiteBoard={allowLiveCollabWhiteBoard}
                        isWhiteboardOpen={isWhiteboardOpen}
                        isPinned={isPinned}
                        setIsPinned={setIsPinned}
                      />
                    ))}
                  {/* Host Controls */}
                  {showHostControl && (
                    <HostControlDrawer
                      showHostControl={showHostControl}
                      setShowHostControl={setShowHostControl}
                      room={room}
                      setForceVideoOff={setForceVideoOff}
                      forceVideoOff={forceVideoOff}
                      setForceMute={setForceMute}
                      forceMute={forceMute}
                      setCanDownloadChatAttachment={
                        setCanDownloadChatAttachment
                      }
                      canDownloadChatAttachment={canDownloadChatAttachment}
                      setAllowLiveCollabWhiteBoard={
                        setAllowLiveCollabWhiteBoard
                      }
                      allowLiveCollabWhiteBoard={allowLiveCollabWhiteBoard}
                      isWhiteboardOpen={isWhiteboardOpen}
                      setDrawerState={setDrawerState}
                    />
                  )}
                  {meetingFeatures?.breakout_room === 1 && showBreakoutRoom && (
                    <BreakoutRoomDrawer
                      showBreakoutRoom={showBreakoutRoom}
                      setShowBreakoutRoom={setShowBreakoutRoom}
                      remoteParticipants={room.remoteParticipants}
                      localParticipant={room.localParticipant}
                      breakoutRooms={breakoutRooms}
                      setBreakoutRooms={setBreakoutRooms}
                      id={props.id}
                      setRoomKeyCounter={setRoomKeyCounter}
                      roomKeyCounter={roomKeyCounter}
                      setToastNotification={setToastNotification}
                      setToastStatus={setToastStatus}
                      setShowToast={setShowToast}
                      breakoutRoomDuration={breakoutRoomDuration}
                      meetingDetails={props.meetingDetails}
                      coHostToken={coHostToken}
                      setDrawerState={setDrawerState}
                      setIsBreakoutRoomCnfigSet={setIsBreakoutRoomCnfigSet}
                    />
                  )}
                  {isVBDrawerOpen && (
                    <VirtualBackgroundDrawer
                      isVBDrawerOpen={isVBDrawerOpen}
                      setIsVBDrawerOpen={setIsVBDrawerOpen}
                      room={room}
                      setToastNotification={setToastNotification}
                      setToastStatus={setToastStatus}
                      setShowToast={setShowToast}
                      setDrawerState={setDrawerState}
                    />
                  )}
                  {isRPDrawerOpen && (
                    <ReportProblemDrawer
                      isRPDrawerOpen={isRPDrawerOpen}
                      setIsRPDrawerOpen={setIsRPDrawerOpen}
                      id={props.id}
                      clientPreferedServerId={props.clientPreferedServerId}
                      isWhiteboardOpen={isWhiteboardOpen}
                      localParticipant={room?.localParticipant}
                      setDrawerState={setDrawerState}
                      setToastNotification={setToastNotification}
                      setToastStatus={setToastStatus}
                      setShowToast={setShowToast}
                    />
                  )}
                </div>
              )
            ) : (
              <div className="lk-focus-layout-wrapper">
                <FocusLayoutContainer>
                  <CarouselLayout tracks={carouselTracks}>
                    <TrackRefContext.Consumer>
                      {(trackRef) => (
                        <ParticipantTile
                          trackRef={trackRef}
                          showEmojiReaction={showEmojiReaction}
                          setShowEmojiReaction={setShowEmojiReaction}
                          showRaiseHand={showRaiseHand}
                          remoteRaisedHands={remoteRaisedHands}
                          remoteEmojiReactions={remoteEmojiReactions}
                          setRemoteEmojiReactions={setRemoteEmojiReactions}
                        />
                      )}
                    </TrackRefContext.Consumer>
                  </CarouselLayout>
                  {focusTrack && (
                    <ParticipantTile
                      trackRef={focusTrack}
                      showEmojiReaction={showEmojiReaction}
                      setShowEmojiReaction={setShowEmojiReaction}
                      showRaiseHand={showRaiseHand}
                      remoteRaisedHands={remoteRaisedHands}
                      remoteEmojiReactions={remoteEmojiReactions}
                      setRemoteEmojiReactions={setRemoteEmojiReactions}
                    />
                  )}
                </FocusLayoutContainer>
                {meetingFeatures?.conference_chat === 1 &&
                  (isMobileBrowser() ? (
                    <ChatsDrawer
                      messageFormatter={formatChatMessageLinks}
                      privatechatparticipants={privatechatparticipants}
                      setprivatechatparticipants={setPrivateChatParticipants}
                      selectedprivatechatparticipant={
                        selectedPrivateChatParticipant
                      }
                      setselectedprivatechatparticipant={
                        setSelectedPrivateChatParticipant
                      }
                      showPrivateChat={showPrivateChat}
                      setShowPrivateChat={setShowPrivateChat}
                      localparticipant={room.localParticipant}
                      privatechatmessages={privatechatmessages}
                      setprivatechatmessages={setPrivateChatMessages}
                      newmessagerender={newMessageRender}
                      show={showChatDrawer}
                      setShow={setShowChatDrawer}
                      setPrivateChatUnreadMessagesCount={
                        setPrivateChatUnreadMessagesCount
                      }
                      privateChatUnreadMessagesCount={
                        privateChatUnreadMessagesCount
                      }
                      setPublicChatUnreadMessagesCount={
                        setPublicChatUnreadMessagesCount
                      }
                      publicChatUnreadMessagesCount={
                        publicChatUnreadMessagesCount
                      }
                      canDownloadChatAttachment={canDownloadChatAttachment}
                      chatMessages={publicChatMessages}
                      setpublicchatmessage={setPublicChatMessages}
                      setToastNotification={setToastNotification}
                      setToastStatus={setToastStatus}
                      setShowToast={setShowToast}
                    />
                  ) : (
                    <ChatsDrawer
                      messageFormatter={formatChatMessageLinks}
                      privatechatparticipants={privatechatparticipants}
                      setprivatechatparticipants={setPrivateChatParticipants}
                      selectedprivatechatparticipant={
                        selectedPrivateChatParticipant
                      }
                      setselectedprivatechatparticipant={
                        setSelectedPrivateChatParticipant
                      }
                      showPrivateChat={showPrivateChat}
                      setShowPrivateChat={setShowPrivateChat}
                      localparticipant={room.localParticipant}
                      privatechatmessages={privatechatmessages}
                      setprivatechatmessages={setPrivateChatMessages}
                      newmessagerender={newMessageRender}
                      show={showChatDrawer}
                      setShow={setShowChatDrawer}
                      setPrivateChatUnreadMessagesCount={
                        setPrivateChatUnreadMessagesCount
                      }
                      privateChatUnreadMessagesCount={
                        privateChatUnreadMessagesCount
                      }
                      setPublicChatUnreadMessagesCount={
                        setPublicChatUnreadMessagesCount
                      }
                      publicChatUnreadMessagesCount={
                        publicChatUnreadMessagesCount
                      }
                      canDownloadChatAttachment={canDownloadChatAttachment}
                      chatMessages={publicChatMessages}
                      setpublicchatmessage={setPublicChatMessages}
                      setToastNotification={setToastNotification}
                      setToastStatus={setToastStatus}
                      setShowToast={setShowToast}
                    />
                  ))}
                {/* {meetingFeatures?.conference_chat === 1 &&
                  (isMobileBrowser() ? (
                    <div
                      className={`lk-chat ${
                        widgetState.showChat && isMobileBrowser()
                          ? "show-chat-mobile"
                          : ""
                      }`}
                      style={{
                        display: widgetState.showChat ? "grid" : "none",
                      }}
                    >
                      <Chat messageFormatter={formatChatMessageLinks} />
                    </div>
                  ) : (
                    <div
                      className={`lk-chat ${
                        widgetState.showChat ? "lk-show-chat" : ""
                      }`}
                      style={{
                        display: widgetState.showChat ? "flex" : "none",
                      }}
                    >
                      <Chat messageFormatter={formatChatMessageLinks} />
                    </div>
                  ))} */}
                {showParticipantsList &&
                  connected &&
                  (isMobileBrowser() ? (
                    <div className="side-drawer-mobile">
                      <ParticipantList
                        remoteParticipants={room.remoteParticipants}
                        localParticipant={room.localParticipant}
                        showParticipantsList={showParticipantsList}
                        setShowParticipantsList={setShowParticipantsList}
                        id={props.id}
                        isHost={props.isHost}
                        layoutContext={layoutContext}
                        isCoHost={isCoHost}
                        lobbyParticipants={lobbyParticipants}
                        setLobbyParticipants={setLobbyParticipants}
                        setRemoteRaisedHands={setRemoteRaisedHands}
                        coHostToken={coHostToken}
                        setDrawerState={setDrawerState}
                        setShowRaiseHand={setShowRaiseHand}
                        currentRoomName={currentRoomName}
                        setToastNotification={setToastNotification}
                        setToastStatus={setToastStatus}
                        setShowToast={setShowToast}
                        setBreakoutRoomDuration={setBreakoutRoomDuration}
                        meetingDetails={props.meetingDetails}
                        breakoutRooms={breakoutRooms}
                        setBreakoutRooms={setBreakoutRooms}
                        setRoomKeyCounter={setRoomKeyCounter}
                        isBreakoutRoomCnfigSet={isBreakoutRoomCnfigSet}
                        setIsBreakoutRoomCnfigSet={setIsBreakoutRoomCnfigSet}
                        meetingFeatures={meetingFeatures}
                        setselectedprivatechatparticipant={
                          setSelectedPrivateChatParticipant
                        }
                        setprivatechatparticipants={setPrivateChatParticipants}
                        privatechatparticipants={privatechatparticipants}
                        setshowprivatechat={setShowPrivateChat}
                        forcemute={forceMute}
                        forcevideooff={forceVideoOff}
                        room={room}
                        isBreakoutRoom={isBreakoutRoom}
                        allowLiveCollabWhiteBoard={allowLiveCollabWhiteBoard}
                        isWhiteboardOpen={isWhiteboardOpen}
                        isPinned={isPinned}
                        setIsPinned={setIsPinned}
                      />
                    </div>
                  ) : (
                    <ParticipantList
                      remoteParticipants={room.remoteParticipants}
                      localParticipant={room.localParticipant}
                      showParticipantsList={showParticipantsList}
                      setShowParticipantsList={setShowParticipantsList}
                      id={props.id}
                      isHost={props.isHost}
                      layoutContext={layoutContext}
                      isCoHost={isCoHost}
                      lobbyParticipants={lobbyParticipants}
                      setLobbyParticipants={setLobbyParticipants}
                      setRemoteRaisedHands={setRemoteRaisedHands}
                      coHostToken={coHostToken}
                      setDrawerState={setDrawerState}
                      breakoutRooms={breakoutRooms}
                      setShowRaiseHand={setShowRaiseHand}
                      currentRoomName={currentRoomName}
                      setToastNotification={setToastNotification}
                      setToastStatus={setToastStatus}
                      setShowToast={setShowToast}
                      setBreakoutRoomDuration={setBreakoutRoomDuration}
                      meetingDetails={props.meetingDetails}
                      setBreakoutRooms={setBreakoutRooms}
                      setRoomKeyCounter={setRoomKeyCounter}
                      isBreakoutRoomCnfigSet={isBreakoutRoomCnfigSet}
                      setIsBreakoutRoomCnfigSet={setIsBreakoutRoomCnfigSet}
                      meetingFeatures={meetingFeatures}
                      setselectedprivatechatparticipant={
                        setSelectedPrivateChatParticipant
                      }
                      setprivatechatparticipants={setPrivateChatParticipants}
                      privatechatparticipants={privatechatparticipants}
                      setshowprivatechat={setShowPrivateChat}
                      forcemute={forceMute}
                      forcevideooff={forceVideoOff}
                      room={room}
                      isBreakoutRoom={isBreakoutRoom}
                      allowLiveCollabWhiteBoard={allowLiveCollabWhiteBoard}
                      isWhiteboardOpen={isWhiteboardOpen}
                      isPinned={isPinned}
                      setIsPinned={setIsPinned}
                    />
                  ))}
                {showHostControl && (
                  <HostControlDrawer
                    showHostControl={showHostControl}
                    setShowHostControl={setShowHostControl}
                    room={room}
                    setForceVideoOff={setForceVideoOff}
                    forceVideoOff={forceVideoOff}
                    setForceMute={setForceMute}
                    forceMute={forceMute}
                    setCanDownloadChatAttachment={setCanDownloadChatAttachment}
                    canDownloadChatAttachment={canDownloadChatAttachment}
                    setAllowLiveCollabWhiteBoard={setAllowLiveCollabWhiteBoard}
                    allowLiveCollabWhiteBoard={allowLiveCollabWhiteBoard}
                    isWhiteboardOpen={isWhiteboardOpen}
                    setDrawerState={setDrawerState}
                  />
                )}
                {/* Live Captions */}
                {meetingFeatures?.voice_transcription === 1 &&
                  isLiveCaptionsDrawerOpen && (
                    <LiveCaptionsDrawer
                      isLiveCaptionsDrawerOpen={isLiveCaptionsDrawerOpen}
                      setIsLiveCaptionsDrawerOpen={setIsLiveCaptionsDrawerOpen}
                      remoteParticipants={room.remoteParticipants}
                      localParticipant={room.localParticipant}
                      liveCaptionData={liveCaptionData}
                      meetingDetails={props.meetingDetails}
                      livecaptionsobject={liveCaptionsObject}
                      setlivecaptionsobject={setLiveCaptionsObject}
                      setfinalcaptions={setFinalCaptions}
                      finalcaptions={finalCaptions}
                      id={props.id}
                      setDrawerState={setDrawerState}
                      translationDetails={translationDetails}
                      setTranslationDetails={setTranslationDetails}
                      finalTranslatedCaptions={finalTranslatedCaptions}
                      setFinalTranslatedCaptions={setFinalTranslatedCaptions}
                      meetingFeatures={meetingFeatures}
                      coHostToken={coHostToken}
                      setLiveCapTourState={setLiveCapTourState}
                      setToastNotification={setToastNotification}
                      setToastStatus={setToastStatus}
                      setShowToast={setShowToast}
                    />
                  )}
                {meetingFeatures?.breakout_room === 1 && showBreakoutRoom && (
                  <BreakoutRoomDrawer
                    showBreakoutRoom={showBreakoutRoom}
                    setShowBreakoutRoom={setShowBreakoutRoom}
                    remoteParticipants={room.remoteParticipants}
                    localParticipant={room.localParticipant}
                    breakoutRooms={breakoutRooms}
                    setBreakoutRooms={setBreakoutRooms}
                    id={props.id}
                    setRoomKeyCounter={setRoomKeyCounter}
                    roomKeyCounter={roomKeyCounter}
                    setToastNotification={setToastNotification}
                    setToastStatus={setToastStatus}
                    setShowToast={setShowToast}
                    breakoutRoomDuration={breakoutRoomDuration}
                    meetingDetails={props.meetingDetails}
                    coHostToken={coHostToken}
                    setDrawerState={setDrawerState}
                    setIsBreakoutRoomCnfigSet={setIsBreakoutRoomCnfigSet}
                  />
                )}
                {isVBDrawerOpen && (
                  <VirtualBackgroundDrawer
                    isVBDrawerOpen={isVBDrawerOpen}
                    setIsVBDrawerOpen={setIsVBDrawerOpen}
                    room={room}
                    setToastNotification={setToastNotification}
                    setToastStatus={setToastStatus}
                    setShowToast={setShowToast}
                    setDrawerState={setDrawerState}
                  />
                )}
                {isRPDrawerOpen && (
                  <ReportProblemDrawer
                    isRPDrawerOpen={isRPDrawerOpen}
                    setIsRPDrawerOpen={setIsRPDrawerOpen}
                    id={props.id}
                    clientPreferedServerId={props.clientPreferedServerId}
                    isWhiteboardOpen={isWhiteboardOpen}
                    localParticipant={room?.localParticipant}
                    setDrawerState={setDrawerState}
                    setToastNotification={setToastNotification}
                    setToastStatus={setToastStatus}
                    setShowToast={setShowToast}
                  />
                )}
                {/* Recording Consent Drawer */}
                {showRecordingConsentDrawer  && (
                    <RecordingConsentDrawer
                      showRecordingConsentDrawer={showRecordingConsentDrawer}
                      setShowRecordingConsentDrawer={setShowRecordingConsentDrawer}
                      participantConsent={participantConsent}
                      setDrawerState={setDrawerState}
                    />
                  )}
              </div>
            )}
            <MeetindEndedModal
              isMeetingEndedOpen={isDisconneted}
              setIsMeetingEndedOpen={setIsDisconnected}
            />
            <ControlBar
              controls={{
                chat: true,
                settings: !!SettingsComponent,
                info: true,
              }}
              showParticipantsList={showParticipantsList}
              setShowParticipantsList={setShowParticipantsList}
              showHostControl={showHostControl}
              setShowHostControl={setShowHostControl}
              showBreakoutRoom={showBreakoutRoom}
              setShowBreakoutRoom={setShowBreakoutRoom}
              showRaiseHand={showRaiseHand}
              setShowRaiseHand={setShowRaiseHand}
              showEmojiReaction={showEmojiReaction}
              setShowEmojiReaction={setShowEmojiReaction}
              widgetUpdate={widgetUpdate}
              room={room}
              id={props.id}
              isHost={props.isHost}
              maxWidth={props.maxWidth}
              maxHeight={props.maxHeight}
              meetingDetails={props.meetingDetails}
              showRecording={showRecording}
              isVBDrawerOpen={isVBDrawerOpen}
              setIsVBDrawerOpen={setIsVBDrawerOpen}
              isRPDrawerOpen={isRPDrawerOpen}
              isLiveCaptionsDrawerOpen={isLiveCaptionsDrawerOpen}
              setIsLiveCaptionsDrawerOpen={setIsLiveCaptionsDrawerOpen}
              setIsRPDrawerOpen={setIsRPDrawerOpen}
              setShowRecording={setShowRecording}
              isCoHost={isCoHost}
              isForceMuteAll={isForceMuteAll}
              isForceVideoOffAll={isForceVideoOffAll}
              coHostToken={coHostToken}
              setDrawerState={setDrawerState}
              drawerState={drawerState}
              isBreakoutRoom={isBreakoutRoom}
              remoteParticipants={room.remoteParticipants}
              localParticipant={room.localParticipant}
              meetingFeatures={meetingFeatures}
              showChatDrawer={showChatDrawer}
              setShowChatDrawer={setShowChatDrawer}
              privateChatUnreadMessagesCount={privateChatUnreadMessagesCount}
              publicChatUnreadMessagesCount={publicChatUnreadMessagesCount}
              showlivecaptionsicon={liveCaptionsObject.showIcon}
              isWebinarMode={isWebinarMode}
              isPIPEnabled={isPIPEnabled}
              setIsPIPEnabled={setIsPIPEnabled}
              sipData={sipData}
              isWhiteboardOpen={isWhiteboardOpen}
              setIsWhiteboardOpen={setIsWhiteboardOpen}
              screenShareSources={screenShareSources}
              isElectronApp={isElectronApp}
              isExitWhiteboardModalOpen={isExitWhiteboardModalOpen}
              setIsExitWhiteboardModalOpen={setIsExitWhiteboardModalOpen}
              whiteboardSceneData={whiteboardSceneData}
              setWhiteboardSceneData={setWhiteboardSceneData}
              whiteBoardId={whiteboardId}
              setWhiteboardId={setWhiteboardId}
              setScreenShareDisplayId={setScreenShareDisplayId}
              isScreenShareEnabled={isScreenShareEnabled}
              setIsScreenShareEnabled={setIsScreenShareEnabled}
              screenShareMode={screenShareMode}
              setScreenShareMode={setScreenShareMode}
              onScreenShareChange={onScreenShareChange}
              setToastNotification={setToastNotification}
              setToastStatus={setToastStatus}
              setShowToast={setShowToast}
              isSelfVideoMirrored={isSelfVideoMirrored}
              setIsSelfVideoMirrored={setIsSelfVideoMirrored}
              deviceIdAudio={deviceIdAudio}
              setDeviceIdAudio={setDeviceIdAudio}
              isRecordingLoading={isRecordingLoading}
              setIsRecordingLoading={setIsRecordingLoading}
              setParticipantConsent={setParticipantConsent}
              setShowRecordingConsentDrawer={setShowRecordingConsentDrawer}
              setShowRecordingConsentIcon={setShowRecordingConsentIcon}
              showRecordingConsentDrawer={showRecordingConsentDrawer}
              screenShareTracks={screenShareTracks}
              focusTrack={focusTrack}
            />
          </div>
          <NotificationModal
            notificationVisible={notificationVisible}
            setNotificationVisible={setNotificationVisible}
            content={notificationContent}
            action={notificationAction}
            room={room}
          />

          {/* <TimerNotification
            timerSeconds={timerSeconds}
            setTimerSeconds={setTimerSeconds}
          /> */}
        </LayoutContextProvider>
      )}
      <RoomAudioRenderer />
      {showToast && (
        // <Toast>
        //   <>{toastNotification}</>
        // </Toast>
        <StatusNotification
          message={toastNotification}
          status={toastStatus}
          // show={showToast}
          setShow={setShowToast}
        />
      )}
      <ConnectionStateToast state={connectionState} />
      <Joyride
        continuous // Keep the tour going until it's ended or closed
        run={liveCapTourState.run}
        showProgress={false} // Hide the progress
        showSkipButton
        stepIndex={liveCapTourState.stepIndex}
        callback={handleJoyrideCallback}
        steps={liveCapTourState.steps}
        styles={{
            options: {
                zIndex: 10000,
            },
            buttonBack: {
              color: "#3B60E4"
            },
            buttonNext: {
              backgroundColor: "#3B60E4",
            },
            buttonClose: {
              display: "none",
            },
            tooltipContent: {
              paddingBottom: "0px",
            }
        }}
        // debug
        disableScrolling
        scrollDuration={1000}
        spotlightPadding={0}
        beaconComponent={false}
      />
      {/* Picture-in-Picture Portal */}
      {pipPortal}
    </div>
  );
}